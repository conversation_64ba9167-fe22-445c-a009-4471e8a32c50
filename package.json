{"name": "tutimi-landing", "private": true, "description": "TuTiMi Tea landing page built with Vite + React + TypeScript", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md,css}\"", "type-check": "tsc --noEmit", "check-build": "node scripts/check-build.js"}, "dependencies": {"embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.18.1", "lucide-react": "^0.513.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "1.3.0", "react-router-dom": "^6.8.0", "react-swipeable": "^7.0.2"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.5", "prettier": "^2.8.8", "tailwindcss": "^3.4.0", "typescript": "^4.9.3", "vite": "^4.1.0"}, "keywords": ["vite", "react", "typescript", "tutimi", "tea", "coffee"], "license": "0BSD", "repository": {"type": "git", "url": "https://github.com/gatsbyjs/gatsby-starter-default"}}