/* Font Configuration - Using SVN-Arial (TuTiMi <PERSON>ont) */

:root {
  /* Colors */
  --primary-navy: #1f4171;
  --secondary-coral: #ff7d3c;
  --secondary-mint: #2ddac0;
  --support-light: #f5f7fa;
  --support-dark: #142d4f;
  --support-soft-orange: #ffeee7;
  --support-soft-mint: #e8fcf9;

  /* Typography - Using SVN-Arial for brand consistency */
  --font-heading: "SVN-Arial", "Arial", -apple-system, BlinkMacSystemFont,
    "Segoe UI", sans-serif;
  --font-body: "SVN-Arial", "Arial", -apple-system, BlinkMacSystemFont,
    "Segoe UI", sans-serif;
  --font-accent: "SVN-Arial", "Arial", -apple-system, BlinkMacSystemFont,
    "Segoe UI", sans-serif;

  /* Type Scale */
  --text-xs: clamp(0.75rem, 0.7vw + 0.6rem, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8vw + 0.75rem, 1rem);
  --text-base: clamp(1rem, 1vw + 0.85rem, 1.125rem);
  --text-lg: clamp(1.125rem, 1.2vw + 0.9rem, 1.25rem);
  --text-xl: clamp(1.25rem, 1.5vw + 1rem, 1.5rem);
  --text-2xl: clamp(1.5rem, 2vw + 1.1rem, 2rem);
  --text-3xl: clamp(2rem, 2.5vw + 1.5rem, 2.5rem);
  --text-4xl: clamp(2.5rem, 3vw + 1.75rem, 3rem);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  font-family: "SVN-Arial", "Arial", -apple-system, BlinkMacSystemFont,
    "Segoe UI", sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--support-dark);
  background-color: var(--support-light);
  font-size: var(--text-base);
  overflow-x: hidden;
}

/* Set default font for all elements */
* {
  font-family: inherit;
}

/* Ensure headings also use the same font family */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "SVN-Arial", "Arial", -apple-system, BlinkMacSystemFont,
    "Segoe UI", sans-serif;
}

/* Layout Structure */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  width: 100%;
}

main {
  flex: 1;
  overflow-x: hidden;
  width: 100%;
}
