import React from "react"

interface TiktokIconProps {
  size?: number
  className?: string
}

const TiktokIcon: React.FC<TiktokIconProps> = ({
  size = 16,
  className = "",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    fill="white"
    className={`bi bi-tiktok ${className}`}
    viewBox="0 0 16 16"
    aria-label="TikTok"
  >
    <path d="M9 0h1.98c.144.715.54 1.617 1.235 2.512C12.895 3.389 13.797 4 15 4v2c-1.753 0-3.07-.814-4-1.829V11a5 5 0 1 1-5-5v2a3 3 0 1 0 3 3z" />
  </svg>
)

export default TiktokIcon
