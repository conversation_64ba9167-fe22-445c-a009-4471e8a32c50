import React from "react"
import { Helmet } from "react-helmet-async"
import { WE<PERSON>IT<PERSON>, ASSETS } from "../constants"

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  children?: React.ReactNode
}

const defaultSEO = {
  title: WEBSITE.TITLE,
  description: WEBSITE.DESCRIPTION,
  keywords: WEBSITE.KEYWORDS,
  image: ASSETS.LOGOS.MAIN,
  url: WEBSITE.DOMAIN,
}

const SEO: React.FC<SEOProps> = ({
  title,
  description,
  keywords,
  image,
  url,
  children,
}) => {
  const seoTitle = title ? `${title} | ${defaultSEO.title}` : defaultSEO.title
  const seoDescription = description || defaultSEO.description
  const seoKeywords = keywords || defaultSEO.keywords
  const seoImage = image || defaultSEO.image
  const seoUrl = url || defaultSEO.url

  return (
    <Helmet>
      {/* Primary Meta Tags */}
      <title>{seoTitle}</title>
      <meta name="title" content={seoTitle} />
      <meta name="description" content={seoDescription} />
      <meta name="keywords" content={seoKeywords} />
      <meta name="author" content={WEBSITE.AUTHOR} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={seoUrl} />
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={seoDescription} />
      <meta property="og:image" content={seoImage} />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={seoUrl} />
      <meta property="twitter:title" content={seoTitle} />
      <meta property="twitter:description" content={seoDescription} />
      <meta property="twitter:image" content={seoImage} />

      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#ffffff" />
      <link rel="canonical" href={seoUrl} />

      {/* Custom children */}
      {children}
    </Helmet>
  )
}

export default SEO
