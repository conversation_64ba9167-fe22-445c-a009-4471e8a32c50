export interface Store {
  id: number
  name: string
  address: string
  phone: string
  hours?: string
  lat?: number
  lng?: number
}

export const stores: Store[] = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>ớ<PERSON>",
    address:
      "129<PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 27, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON>",
    phone: "0562628240",
    lat: 10.818691156834745,
    lng: 106.72288800277147,
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    address: "376 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> 3, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>",
    phone: "0909815428",
    lat: 10.79990288269392,
    lng: 106.68011838145337,
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    address:
      "141 <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> 26, <PERSON><PERSON><PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>",
    phone: "0909462558",
    lat: 10.808902910484525,
    lng: 106.70909922683957,
  },
  {
    id: 4,
    name: "<PERSON><PERSON><PERSON><PERSON> 8",
    address: "202 <PERSON><PERSON><PERSON>ng <PERSON><PERSON> 8, <PERSON><PERSON>ng 11, <PERSON>u<PERSON>n <PERSON><PERSON> <PERSON><PERSON>p, <PERSON>h<PERSON>nh ph<PERSON> <PERSON><PERSON> <PERSON><PERSON> <PERSON>",
    phone: "0764282047",
    lat: 10.843977205677566,
    lng: 106.66199149040315,
  },
  {
    id: 5,
    name: "<PERSON>an V<PERSON>n Tr<PERSON>",
    address: "711 <PERSON>an <PERSON><PERSON>n Tr<PERSON>, <PERSON><PERSON><PERSON>ng 7, Quận Gò Vấp,  Thành phố Hồ Chí Minh",
    phone: "0909814958",
    lat: 10.827789234237267,
    lng: 106.68657021643583,
  },
  {
    id: 6,
    name: "Ngô Tất Tố",
    address: "71 Ngô Tất Tố, Phường 21, Quận Bình Thạnh, Thành phố Hồ Chí Minh",
    phone: "0909472958",
    lat: 10.8156,
    lng: 106.7234,
  },
  {
    id: 7,
    name: "Nguyễn Văn Quá",
    address:
      "966 Đường Nguyễn Văn Quá, Đông Hưng Thuận, Quận 12, Thành phố Hồ Chí Minh",
    phone: "0584984031",
    lat: 10.8567,
    lng: 106.6234,
  },
  {
    id: 8,
    name: "Vạn Kiếp",
    address: "97a Vạn Kiếp, Phường 3, Quận Bình Thạnh,  Thành phố Hồ Chí Minh",
    phone: "0909987418",
    lat: 10.8089,
    lng: 106.7098,
  },
  {
    id: 9,
    name: "Tân Quý",
    address: "34c Tân Quý, Phường Tân Quý, Quận Tân Phú, Thành phố Hồ Chí Minh",
    phone: "0395850994",
    lat: 10.7923,
    lng: 106.6234,
  },
  {
    id: 10,
    name: "Đồng Đen",
    address: "39 Đồng Đen, Phường 12, Quận Tân Bình,  Thành phố Hồ Chí Minh",
    phone: "0707528559",
    lat: 10.7834,
    lng: 106.6456,
  },
  {
    id: 11,
    name: "71A Nguyễn Văn Quá",
    address:
      "71a Nguyễn Văn Quá, Phường Tân Hưng Thuận, Quận 12,  Thành phố Hồ Chí Minh",
    phone: "0584793279",
  },

  {
    id: 13,
    name: "Lạc Long Quân",
    address:
      "712 Lạc Long Quân, Phường 9, Quận Tân Bình,  Thành phố Hồ Chí Minh",
    phone: "0762980137",
  },
  {
    id: 14,
    name: "Đặng Thuỳ Trâm",
    address:
      "39/50 Đặng Thuỳ Trâm, Phường 13, Quận Bình Thạnh,  Thành phố Hồ Chí Minh",
    phone: "0563247332",
  },
  {
    id: 15,
    name: "Quang Trung",
    address:
      "129 Quang Trung, Thị Trấn Hóc Môn, Huyện Hóc Môn,  Thành phố Hồ Chí Minh",
    phone: "0562732641",
  },
  {
    id: 16,
    name: "Tỉnh Lộ 10",
    address: "329 Tỉnh Lộ 10, P. An Lạc, Quận Bình Tân,  Thành phố Hồ Chí Minh",
    phone: "0589855674",
  },
  {
    id: 17,
    name: "Hiệp Bình",
    address:
      "77a Hiệp Bình Phường Hiệp Bình Phước, Thành Phố Thủ Đức, Thành phố Hồ Chí Minh",
    phone: "0909986594",
  },
  {
    id: 18,
    name: "Nguyễn Duy Trinh",
    address:
      "168a Nguyễn Duy Trinh, Phường Bình Trưng Tây, Thành Phố Thủ Đức,  Thành phố Hồ Chí Minh",
    phone: "0563245841",
  },
  {
    id: 19,
    name: "Cách Mạng Tháng Tám",
    address:
      "376d Cách Mạng Tháng Tám, Phường 10, Quận 3,  Thành phố Hồ Chí Minh",
    phone: "0589855660",
  },
  {
    id: 20,
    name: "Đường Số 17, Thủ Đức",
    address:
      "38a Đường Số 17, Phường Linh Chiểu, Thành Phố Thủ Đức,  Thành phố Hồ Chí Minh",
    phone: "0795561505",
  },
  {
    id: 21,
    name: "Trường Sơn",
    address: "65 Trường Sơn, Phường 2, Quận Tân Bình,  Thành phố Hồ Chí Minh",
    phone: "0909144618",
  },
  {
    id: 22,
    name: "Đỗ Xuân Hợp",
    address:
      "275 Đỗ Xuân Hợp, Phường Phước Long B, Thành Phố Thủ Đức,  Thành phố Hồ Chí Minh",
    phone: "0587699857",
  },
  {
    id: 23,
    name: "Đường Số 3",
    address:
      "59 Đường Số 3, Phường An Khánh, Thành Phố Thủ Đức, Thành phố Hồ Chí Minh",
    phone: "0333176264",
  },
  {
    id: 24,
    name: "Nguyễn Tri Phương",
    address: "372 Nguyễn Tri Phương Phường 4, Quận 10,  Thành phố Hồ Chí Minh",
    phone: "0933387161",
  },
  {
    id: 25,
    name: "Vĩnh Hội",
    address: "98 Vĩnh Hội, Phường 4, Quận 4, Thành phố Hồ Chí Minh",
    phone: "0909471528",
  },
  {
    id: 26,
    name: "Lê Văn Quới",
    address:
      "572 Lê Văn Quới, Phường Bình Hưng Hòa A, Quận Bình Tân,  Thành phố Hồ Chí Minh",
    phone: "0395903141",
  },
  {
    id: 27,
    name: "Tân Kiểng",
    address: "26 Đường Số 17, Phường Tân Kiểng, Quận 7,  Thành phố Hồ Chí Minh",
    phone: "0582979262",
  },
  {
    id: 28,
    name: "Man Thiện",
    address:
      "45 Man Thiện Phường Tân Hiệp Phú, Thành Phố Thủ Đức, Thành phố Hồ Chí Minh",
    phone: "0773821294",
  },
  {
    id: 29,
    name: "Đặng Chất",
    address: "45 Đặng Chất, Phường 2, Quận 8,  Thành phố Hồ Chí Minh",
    phone: "0924895328",
  },
  {
    id: 30,
    name: "Lê Thị Riêng",
    address: "60h Lê Thị Riêng, P. Bến Thành, Quận 1,  Thành phố Hồ Chí Minh",
    phone: "0901837854",
  },
  {
    id: 31,
    name: "Hồ Học Lãm",
    address:
      "486A Hồ Học Lãm, phường An Lạc, Quận Bình Tân, Thành phố Hồ Chí Minh",
    phone: "0909462558",
  },
]
