import React from "react"
import { <PERSON><PERSON>tFooter } from "@/components/katinat"

/**
 * Demo page for KATINAT Footer Component
 * Shows the footer component in isolation for testing and preview
 */
const KatinatFooterDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Demo Content */}
      <div className="py-20 text-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            KATINAT Footer Component Demo
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            React component với Tailwind CSS - 100% visual fidelity
          </p>
          <div className="bg-white rounded-lg shadow-lg p-8 text-left">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Tính năng:</h2>
            <ul className="space-y-2 text-gray-700">
              <li>✅ Chuyển đổi hoàn toàn sang Tailwind CSS</li>
              <li>✅ Thay thế FontAwesome bằng Lucide React icons</li>
              <li>✅ Responsive design (mobile-first)</li>
              <li>✅ Accessibility với ARIA labels</li>
              <li>✅ TypeScript support</li>
              <li>✅ Semantic HTML structure</li>
              <li>✅ Hover effects và transitions</li>
              <li>✅ 100% visual fidelity với design gốc</li>
            </ul>
          </div>
        </div>
      </div>

      {/* KATINAT Footer Component */}
      <KatinatFooter />
    </div>
  )
}

export default KatinatFooterDemo
