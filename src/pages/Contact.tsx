import React from "react"
import { Helmet } from "react-helmet-async"
import { motion } from "framer-motion"
import { Phone, FacebookIcon, Users, Briefcase } from "lucide-react"
import {
  Section,
  Container,
  LiquidGlassCard,
  LiquidGlassButton,
} from "../features/he-thong-cua-hang/shared/LiquidGlassComponents"
import { useScrollAnimation } from "../hooks/useScrollAnimation"
import { SOCIAL_MEDIA, CONTACT } from "../constants"

/**
 * Contact Page Component
 * Full page implementation using the same contact styling and functionality from store system pages
 */
const ContactPage: React.FC = () => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })

  return (
    <>
      <Helmet>
        <title>Liên hệ - TuTiMi Tea</title>
        <meta
          name="description"
          content="Liên hệ với TuTiMi Tea qua Facebook, hotline hoặc tuyển dụng. Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7."
        />
        <meta
          name="keywords"
          content="TuTiMi liên hệ, hotline TuTiMi, Facebook TuTiMi, tuyển dụng TuTiMi"
        />
      </Helmet>

      <Section
        className={`relative overflow-hidden py-20 min-h-screen flex items-center justify-center`}
        id="contact-page"
      >
        {/* Enhanced Background with Liquid Glass Effects */}
        <div className="absolute inset-0">
          {/* Primary gradient background matching TuTiMi color scheme */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50/40 via-blue-50/30 to-primary-navy/5" />

          {/* Floating glass orbs for depth */}
          <div className="absolute top-20 left-1/4 w-96 h-96 bg-gradient-to-r from-primary-navy/8 to-secondary-mint/8 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-20 right-1/4 w-80 h-80 bg-gradient-to-r from-secondary-mint/8 to-primary-navy/8 rounded-full blur-3xl animate-pulse delay-1000" />
        </div>

        <Container maxWidth="7xl" className="relative z-10">
          {/* Page Title */}
          <motion.div
            ref={ref}
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12 md:mb-16"
          >
            <h1 className="text-5xl md:text-6xl lg:text-[59px] font-bold text-primary-navy leading-none mb-8">
              LIÊN HỆ VỚI CHÚNG TÔI
            </h1>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
              Chúng tôi luôn sẵn sàng lắng nghe và hỗ trợ bạn. Hãy kết nối với
              TuTiMi qua các kênh liên lạc dưới đây.
            </p>
          </motion.div>

          {/* Contact Cards Grid */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {/* Column 1: Facebook Main Page */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 40 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <LiquidGlassCard
                className="p-6 sm:p-8 text-center h-[380px] sm:h-[420px] flex flex-col justify-between"
                hover={true}
              >
                {/* Icon */}
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary-navy rounded-full flex items-center justify-center">
                    <FacebookIcon
                      size={20}
                      className="text-white sm:w-6 sm:h-6"
                    />
                  </div>
                </div>

                {/* Content */}
                <div className="flex-grow flex flex-col justify-start min-h-[200px]">
                  <div className="h-[80px] flex flex-col justify-start">
                    <p className="text-sm sm:text-base text-gray-600 mb-2">
                      Theo dõi chúng tôi
                    </p>
                    <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3">
                      TuTiMi FanPage
                    </h3>
                  </div>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    @trasuatutimi
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    Cập nhật menu mới - Khuyến mãi hấp dẫn
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    Tương tác với cộng đồng yêu trà sữa
                  </p>
                </div>

                {/* Button */}
                <LiquidGlassButton
                  href={SOCIAL_MEDIA.FACEBOOK.PAGE}
                  variant="primary"
                  className="w-full text-xs sm:text-sm"
                  aria-label="Theo dõi TuTiMi FanPage"
                >
                  <Users size={16} className="sm:w-[18px] sm:h-[18px]" />
                  Theo dõi
                </LiquidGlassButton>
              </LiquidGlassCard>
            </motion.div>

            {/* Column 2: Facebook Recruitment */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 40 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
              transition={{ duration: 0.6, delay: 0.7 }}
            >
              <LiquidGlassCard
                className="p-6 sm:p-8 text-center h-[380px] sm:h-[420px] flex flex-col justify-between"
                hover={true}
              >
                {/* Icon */}
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary-navy rounded-full flex items-center justify-center">
                    <Briefcase size={20} className="text-white sm:w-6 sm:h-6" />
                  </div>
                </div>

                {/* Content */}
                <div className="flex-grow flex flex-col justify-start min-h-[200px]">
                  <div className="h-[80px] flex flex-col justify-start">
                    <p className="text-sm sm:text-base text-gray-600 mb-2">
                      Tham gia với chúng tôi
                    </p>
                    <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3">
                      TuTiMi Tuyển Dụng
                    </h3>
                  </div>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    @tutimituyendung
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    Cơ hội việc làm - Môi trường thân thiện
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    Phát triển sự nghiệp cùng TuTiMi
                  </p>
                </div>

                {/* Button */}
                <LiquidGlassButton
                  href={SOCIAL_MEDIA.FACEBOOK.RECRUITMENT}
                  variant="primary"
                  className="w-full text-xs sm:text-sm"
                  aria-label="Theo dõi TuTiMi Tuyển Dụng"
                >
                  <Briefcase size={16} className="sm:w-[18px] sm:h-[18px]" />
                  Theo dõi
                </LiquidGlassButton>
              </LiquidGlassCard>
            </motion.div>

            {/* Column 3: Hotline 1 */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 40 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              <LiquidGlassCard
                className="p-6 sm:p-8 text-center h-[380px] sm:h-[420px] flex flex-col justify-between"
                hover={true}
              >
                {/* Icon */}
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary-navy rounded-full flex items-center justify-center">
                    <Phone size={20} className="text-white sm:w-6 sm:h-6" />
                  </div>
                </div>

                {/* Content */}
                <div className="flex-grow flex flex-col justify-start min-h-[200px]">
                  <div className="h-[80px] flex flex-col justify-start">
                    <p className="text-sm sm:text-base text-gray-600 mb-2">
                      Gọi ngay
                    </p>
                    <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3">
                      {CONTACT.PHONE}
                    </h3>
                  </div>
                  <div className="text-xs sm:text-sm text-gray-500 mb-4 h-4"></div>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4 h-[40px]">
                    Hỗ trợ 24/7 - Tư vấn miễn phí
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    Đặt hàng và giải đáp thắc mắc
                  </p>
                </div>

                {/* Button */}
                <LiquidGlassButton
                  href={CONTACT.PHONE_URL}
                  variant="primary"
                  className="w-full text-xs sm:text-sm"
                  aria-label="Gọi hotline TuTiMi"
                >
                  <Phone size={16} className="sm:w-[18px] sm:h-[18px]" />
                  Gọi ngay
                </LiquidGlassButton>
              </LiquidGlassCard>
            </motion.div>

            {/* Column 4: Hotline 2 */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 40 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
              transition={{ duration: 0.6, delay: 1.2 }}
            >
              <LiquidGlassCard
                className="p-6 sm:p-8 text-center h-[380px] sm:h-[420px] flex flex-col justify-between"
                hover={true}
              >
                {/* Icon */}
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary-navy rounded-full flex items-center justify-center">
                    <Phone size={20} className="text-white sm:w-6 sm:h-6" />
                  </div>
                </div>

                {/* Content */}
                <div className="flex-grow flex flex-col justify-start min-h-[200px]">
                  <div className="h-[80px] flex flex-col justify-start">
                    <p className="text-sm sm:text-base text-gray-600 mb-2">
                      Gọi ngay
                    </p>
                    <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3">
                      {CONTACT.PHONE_2}
                    </h3>
                  </div>
                  <div className="text-xs sm:text-sm text-gray-500 mb-4 h-4"></div>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4 h-[40px]">
                    Hỗ trợ 24/7 - Tư vấn miễn phí
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mb-4">
                    Đặt hàng và giải đáp thắc mắc
                  </p>
                </div>

                {/* Button */}
                <LiquidGlassButton
                  href={CONTACT.PHONE_2_URL}
                  variant="primary"
                  className="w-full text-xs sm:text-sm"
                  aria-label="Gọi hotline TuTiMi"
                >
                  <Phone size={16} className="sm:w-[18px] sm:h-[18px]" />
                  Gọi ngay
                </LiquidGlassButton>
              </LiquidGlassCard>
            </motion.div>
          </motion.div>
        </Container>
      </Section>
    </>
  )
}

export default ContactPage
