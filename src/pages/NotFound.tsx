import React from "react"
import { Link } from "react-router-dom"
import SEO from "../components/SEO"
import { ROUTES } from "../constants"

const NotFound: React.FC = () => {
  return (
    <>
      <SEO
        title="404: Không Tìm Thấy <PERSON>"
        description="Trang bạn đang tìm kiếm không tồn tại. Quay lại trang chủ TuTiMi để khám phá thêm."
        keywords="404, not found, TuTiMi, error page"
      />
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "60vh",
          textAlign: "center",
          padding: "2rem",
        }}
      >
        <h1
          style={{
            fontSize: "4rem",
            color: "#1f4171",
            marginBottom: "1rem",
            fontWeight: "bold",
          }}
        >
          404
        </h1>
        <h2
          style={{
            fontSize: "1.5rem",
            color: "#142d4f",
            marginBottom: "1rem",
          }}
        >
          <PERSON><PERSON><PERSON><PERSON> Tìm <PERSON>
        </h2>
        <p
          style={{
            fontSize: "1.1rem",
            color: "#666",
            marginBottom: "2rem",
            maxWidth: "500px",
          }}
        >
          Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển. Hãy quay
          lại trang chủ để khám phá thêm về TuTiMi.
        </p>
        <Link
          to={ROUTES.HETHONGCUAHANG}
          style={{
            backgroundColor: "#ff7d3c",
            color: "white",
            padding: "12px 24px",
            borderRadius: "8px",
            textDecoration: "none",
            fontSize: "1rem",
            fontWeight: "600",
            transition: "background-color 0.3s ease",
          }}
          onMouseOver={e => (e.currentTarget.style.backgroundColor = "#e66a2e")}
          onMouseOut={e => (e.currentTarget.style.backgroundColor = "#ff7d3c")}
        >
          Về Trang Chủ
        </Link>
      </div>
    </>
  )
}

export default NotFound
