@import "./fonts.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Embla Carousel optimizations for mobile touch */
.embla {
  overflow: hidden;
}

.embla__container {
  display: flex;
  touch-action: pan-y pinch-zoom;
}

.embla__slide {
  transform: translate3d(0, 0, 0);
  flex: 0 0 100%;
  min-width: 0;
}

/* Mobile-specific carousel optimizations */
@media (max-width: 1024px) {
  .embla__slide {
    flex: 0 0 100%;
  }
}

@media (max-width: 640px) {
  .embla__slide {
    flex: 0 0 100%;
  }
}

/* Touch optimization for better mobile performance */
.touch-optimized {
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y pinch-zoom;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Ensure proper mobile carousel spacing */
@media (max-width: 640px) {
  .embla__slide {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .embla__container {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
}

/* Custom base styles */
@layer base {
  html {
    @apply overflow-x-hidden;
  }

  body {
    @apply overflow-x-hidden text-support-dark bg-support-light;
    font-family: "SVN-Arial", "Arial", -apple-system, BlinkMacSystemFont,
      "Segoe UI", sans-serif;
  }

  #root {
    @apply min-h-screen flex flex-col overflow-x-hidden w-full;
  }

  main {
    @apply flex-1 overflow-x-hidden w-full;
  }
}

/* Custom utility classes */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .bg-navy-gradient {
    background: linear-gradient(135deg, #1f4171, #2a5694);
  }

  .bg-mint-gradient {
    background: linear-gradient(135deg, #2ddac0, #5ee6d1);
  }

  .bg-coral-gradient {
    background: linear-gradient(135deg, #ff7d3c, #ff9d6c);
  }

  /* Custom scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  .scrollbar-thumb-gray-300 {
    scrollbar-color: #d1d5db #f9fafb;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #d1d5db;
  }

  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }

  .scrollbar-track-gray-100 {
    scrollbar-color: #d1d5db #f3f4f6;
  }

  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: #f3f4f6;
  }
}
