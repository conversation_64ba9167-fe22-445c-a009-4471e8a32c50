import React from "react"
import {
  <PERSON><PERSON><PERSON><PERSON>outer as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom"
import { HelmetProvider } from "react-helmet-async"
import { WebTTMI } from "./features/web-ttmi"
import HeT<PERSON><PERSON><PERSON>Hang from "./pages/HeThong<PERSON>uaHang"
import About from "./pages/About"
import Menu from "./pages/Menu"
import Contact from "./pages/Contact"
import NotFound from "./pages/NotFound"
import { ROUTES } from "./constants"
import HomePage from "./features/homepage"
import Partner from "./features/partner"
import CuaHang from "./features/store"

const App: React.FC = () => {
  return (
    <HelmetProvider>
      <Router>
        <Routes>
          <Route
            path={ROUTES.HOME}
            element={
              <WebTTMI>
                <HomePage />
              </WebTTMI>
            }
          />
          <Route path={ROUTES.HETHONGCUAHANG} element={<HeThongCuaHang />} />
          <Route
            path={ROUTES.STORE_LOCATOR}
            element={
              <WebTTMI>
                <CuaHang />
              </WebTTMI>
            }
          />
          <Route
            path={ROUTES.ABOUT}
            element={
              <WebTTMI>
                <About />
              </WebTTMI>
            }
          />
          <Route
            path={ROUTES.MENU}
            element={
              <WebTTMI>
                <Menu />
              </WebTTMI>
            }
          />{" "}
          <Route
            path={ROUTES.PARTNER}
            element={
              <WebTTMI>
                <Partner />
              </WebTTMI>
            }
          />
          <Route
            path={ROUTES.CONTACT}
            element={
              <WebTTMI>
                <Contact />
              </WebTTMI>
            }
          />
          <Route
            path={ROUTES.NOT_FOUND}
            element={
              <WebTTMI>
                <NotFound />
              </WebTTMI>
            }
          />
        </Routes>
      </Router>
    </HelmetProvider>
  )
}

export default App
