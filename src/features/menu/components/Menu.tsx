import React from "react"
import { <PERSON>, Percent, Star } from "lucide-react"
import { LiquidGlassButton } from "../../he-thong-cua-hang/shared/LiquidGlassComponents"

interface MenuProps {
  className?: string
}

const Menu: React.FC<MenuProps> = () => {
  return (
    <div>
      {/* Menu Section */}
      <div className="border-b-2 border-gray-200 pb-12">
        <div className="relative">
          <img
            src="/images/menu.jpg"
            alt="Thực đơn TuTiMi - Menu đồ uống đa dạng"
            className="w-full h-auto rounded-lg sm:rounded-xl"
            loading="lazy"
          />
        </div>
        <div className="text-center font-semibold italic my-12 sm:max-w-[90%] lg:max-w-[60%] mx-auto">
          <p>
            Để đảm bảo trải nghiệm tốt nhất, <PERSON>u<PERSON> khách vui lòng thông báo trước
            cho nhân viên về các thành phần thực phẩm có thể gây dị ứng hoặc ảnh
            hưởng sức khoẻ quý khách.
          </p>
          <p className="mt-4">
            Đồng thời, TUTIMI khuyến nghị Quý khách nên thưởng thức đồ uống
            trong vòng 2 giờ đầu tiên kể từ khi nhận thức uống tại quầy. Việc để
            đồ uống quá lâu hoặc vận chuyển với thời gian dài có thể ảnh hưởng
            đến hương vị và chất lượng của sản phẩm.
          </p>
          <p className="mt-4">
            TUTIMI không chịu trách nhiệm đảm bảo chất lượng sản phẩm trong
            trường hợp các sản phẩm pha chế được dùng sau 2h kể từ khi rời quầy.
          </p>
        </div>
      </div>

      {/* Promotion Section */}
      <div className="py-8 sm:py-12 lg:py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Section Header */}
          <div className="text-center mb-8 sm:mb-10 lg:mb-12">
            <div className="flex flex-col sm:flex-row items-center justify-center mb-4 sm:mb-6">
              <Gift className="w-6 h-6 sm:w-8 sm:h-8 text-primary-navy mb-2 sm:mb-0 sm:mr-3" />
              <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-primary-navy text-center">
                CHƯƠNG TRÌNH KHUYẾN MÃI
              </h2>
              <Gift className="w-6 h-6 sm:w-8 sm:h-8 text-primary-navy mt-2 sm:mt-0 sm:ml-3" />
            </div>
            <p className="text-base sm:text-lg text-support-dark max-w-2xl mx-auto px-2">
              Đừng bỏ lỡ những ưu đãi hấp dẫn từ TuTiMi dành riêng cho bạn
            </p>
          </div>

          {/* Promotions Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
            {/* Promotion 1 */}
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
              <div className="bg-primary-navy p-4 sm:p-6 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Star className="w-6 h-6 sm:w-8 sm:h-8 mr-2 sm:mr-3" />
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-bold">KHUYẾN MÃI 1</h3>
                  </div>
                  <div className="bg-white/20 px-2 sm:px-3 py-1 rounded-full">
                    <span className="text-xs sm:text-sm font-semibold">HOT</span>
                  </div>
                </div>
              </div>
              <div className="p-4 sm:p-6 lg:p-8">
                <div className="text-center mb-4 sm:mb-6">
                  <h4 className="text-2xl sm:text-3xl font-bold text-primary-navy mb-2">
                    Mua 7 ly Tặng 1
                  </h4>
                  <p className="text-gray-600 text-base sm:text-lg">
                    (Áp dụng thẻ tích điểm)
                  </p>
                </div>
                <div className="bg-gray-50 rounded-lg sm:rounded-xl p-4 sm:p-6 border-l-4 border-primary-navy">
                  <p className="text-support-dark leading-relaxed text-sm sm:text-base">
                    Tích điểm mỗi lần mua hàng và nhận ngay ly thứ 8 miễn phí
                    khi đủ điều kiện. Chương trình áp dụng cho tất cả các loại
                    đồ uống tại TuTiMi.
                  </p>
                </div>
              </div>
            </div>

            {/* Promotion 2 */}
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
              <div className="bg-primary-navy p-4 sm:p-6 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Percent className="w-6 h-6 sm:w-8 sm:h-8 mr-2 sm:mr-3" />
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-bold">KHUYẾN MÃI 2</h3>
                  </div>
                  <div className="bg-white/20 px-2 sm:px-3 py-1 rounded-full">
                    <span className="text-xs sm:text-sm font-semibold">NEW</span>
                  </div>
                </div>
              </div>
              <div className="p-4 sm:p-6 lg:p-8">
                <div className="text-center mb-4 sm:mb-6">
                  <h4 className="text-2xl sm:text-3xl font-bold text-primary-navy mb-2 sm:mb-4">
                    Giảm giá theo giá trị đơn hàng
                  </h4>
                </div>
                <div className="space-y-3 sm:space-y-4">
                  <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border-l-4 border-primary-navy">
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-support-dark text-sm sm:text-base">
                        600.000đ - 899.999đ
                      </span>
                      <span className="bg-primary-navy text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-bold">
                        -10%
                      </span>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-3 sm:p-4 border-l-4 border-primary-navy">
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-support-dark text-sm sm:text-base">
                        900.000đ - 1.499.999đ
                      </span>
                      <span className="bg-primary-navy text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-bold">
                        -15%
                      </span>
                    </div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-3 sm:p-4 border-l-4 border-primary-navy">
                    <div className="flex items-center justify-between">
                      <span className="font-semibold text-support-dark text-sm sm:text-base">
                        Từ 1.500.000đ trở lên
                      </span>
                      <span className="bg-primary-navy text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-bold">
                        -20%
                      </span>
                    </div>
                  </div>
                </div>
                <div className="mt-4 sm:mt-6 bg-gray-50 rounded-lg p-3 sm:p-4">
                  <p className="text-xs sm:text-sm text-support-dark text-center">
                    <strong>Lưu ý:</strong> Giảm giá trực tiếp trên tổng giá trị
                    đơn hàng
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-8 sm:mt-10 lg:mt-12">
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8 max-w-2xl mx-auto">
              <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-primary-navy mb-3 sm:mb-4">
                Áp dụng ngay hôm nay!
              </h3>
              <p className="text-support-dark mb-4 sm:mb-6 text-sm sm:text-base px-2">
                Ghé thăm cửa hàng TuTiMi gần nhất để tận hưởng những ưu đãi hấp
                dẫn
              </p>
              <div className="flex justify-center">
                <LiquidGlassButton
                  href="/cua-hang"
                  variant="primary"
                  className="text-sm sm:text-base lg:text-lg px-4 py-2.5 sm:px-6 sm:py-3 lg:px-8 lg:py-4 w-full sm:w-auto max-w-xs"
                >
                  Tìm cửa hàng gần nhất
                </LiquidGlassButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Menu
