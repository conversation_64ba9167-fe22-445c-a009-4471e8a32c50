import React, { useState } from "react"
import { MapPin, ExternalLink, Maximize2, Map, Target } from "lucide-react"
import { Store } from "../../../constants/stores"

interface StoreMapProps {
  stores: Store[]
  selectedStore?: Store | null
  onStoreSelect?: (store: Store) => void
}

const StoreMap: React.FC<StoreMapProps> = ({
  stores,
  selectedStore: externalSelectedStore,
  onStoreSelect,
}) => {
  const [mapMode, setMapMode] = useState<"single" | "multi" | "individual">(
    "single"
  )
  const [internalSelectedStore, setInternalSelectedStore] =
    useState<Store | null>(null)

  // Use external selectedStore if provided, otherwise use internal state
  const selectedStore = externalSelectedStore || internalSelectedStore

  // Create search URL for opening in new tab with all markers
  const createSearchUrl = () => {
    if (stores.length === 0)
      return "https://www.google.com/maps/search/TuTiMi+tea+Ho+Chi+Minh+City"

    if (stores.length === 1) {
      return `https://www.google.com/maps/search/${encodeURIComponent(
        stores[0].address
      )}`
    } else {
      // Create a URL that shows all store locations
      const addresses = stores.map(store => store.address).join(" | ")
      return `https://www.google.com/maps/search/${encodeURIComponent(
        addresses
      )}`
    }
  }

  const handleOpenInNewTab = () => {
    const url =
      createSearchUrl() ||
      "https://www.google.com/maps/search/TuTiMi+tea+Ho+Chi+Minh+City"
    window.open(url, "_blank", "noopener,noreferrer")
  }

  const handleStoreClick = (store: Store) => {
    if (onStoreSelect) {
      onStoreSelect(store)
    } else {
      setInternalSelectedStore(store)
    }
    setMapMode("single")
  }

  const handleMapModeToggle = () => {
    if (mapMode === "single") {
      setMapMode("multi")
    } else if (mapMode === "multi") {
      setMapMode("individual")
    } else {
      setMapMode("single")
      if (onStoreSelect) {
        onStoreSelect(null as any) // Clear external selection
      } else {
        setInternalSelectedStore(null)
      }
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-[#1f4171]/10 rounded-full flex items-center justify-center">
            <MapPin size={18} className="text-[#1f4171]" />
          </div>
          <h2 className="text-xl font-bold text-gray-900">Bản đồ cửa hàng</h2>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleOpenInNewTab}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border-2 border-gray-200 rounded-xl hover:bg-gray-50 hover:border-[#1f4171]/30 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <ExternalLink size={16} />
            Mở rộng
          </button>
        </div>
      </div>

      {/* Map Container */}
      <div className="relative bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden flex-1 flex flex-col">
        {/* Map Frame */}
        <div className="relative flex-1 overflow-hidden">
          <iframe
            src={
              selectedStore && selectedStore.lat && selectedStore.lng
                ? `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1000!2d${
                    selectedStore.lng
                  }!3d${
                    selectedStore.lat
                  }!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2z${encodeURIComponent(
                    selectedStore.name
                  )}!5e0!3m2!1sen!2s!4v1635000000000!5m2!1sen!2s&q=${
                    selectedStore.lat
                  },${selectedStore.lng}+(${encodeURIComponent(
                    selectedStore.name
                  )})`
                : `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d62702.95!2d106.6797!3d10.8231!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f11.1!3m3!1m2!1s0x317529292e8d3dd1%3A0xf15f5aad773c112b!2sHo%20Chi%20Minh%20City%2C%20Vietnam!5e0!3m2!1sen!2s!4v1635000000000!5m2!1sen!2s`
            }
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            title={
              selectedStore
                ? `Bản đồ vị trí ${selectedStore.name}`
                : "Bản đồ vị trí cửa hàng TuTiMi"
            }
            className="w-full h-full"
          />

          {/* Custom Marker Overlay for Selected Store */}
          {selectedStore && selectedStore.lat && selectedStore.lng && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20 pointer-events-none">
              <div className="relative">
                {/* Main Marker */}
                <div className="w-8 h-8 bg-red-500 border-2 border-white rounded-full shadow-lg flex items-center justify-center animate-pulse">
                  <MapPin size={16} className="text-white" />
                </div>

                {/* Marker Label */}
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2">
                  <div className="bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg px-3 py-2 shadow-lg max-w-48">
                    <div className="text-xs font-semibold text-gray-900 mb-1">
                      {selectedStore.name}
                    </div>
                    <div className="text-xs text-gray-600">
                      📍 {selectedStore.address}
                    </div>
                    {/* Arrow pointing to marker */}
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-white"></div>
                  </div>
                </div>

                {/* Pulsing Ring Effect */}
                <div className="absolute inset-0 w-8 h-8 bg-red-500/30 rounded-full animate-ping"></div>
              </div>
            </div>
          )}

          {/* Map Controls Overlay */}
          <div className="absolute top-4 right-4 z-30">
            <button
              onClick={handleOpenInNewTab}
              className="bg-white shadow-lg border border-gray-200 rounded-lg p-2 hover:bg-gray-50 transition-colors duration-200"
              title="Mở bản đồ trong tab mới"
            >
              <Maximize2 size={16} className="text-gray-600" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StoreMap
