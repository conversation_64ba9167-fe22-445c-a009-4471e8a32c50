import React, { useState, useMemo } from "react"
import { MapPin } from "lucide-react"
import { stores, Store } from "../../../constants/stores"
import StoreSearchForm from "./StoreSearchForm"
import StoreList from "./StoreList"
import StoreMap from "./StoreMap"

// Extract unique districts and cities from stores data
const getLocationsFromStores = () => {
  const cities = new Set<string>()
  const districts = new Set<string>()

  stores.forEach(store => {
    const addressParts = store.address.split(", ")
    // Extract city (usually the last part)
    const city = addressParts[addressParts.length - 1]
    if (city) cities.add(city.trim())

    // Extract district (usually contains "Quận" or "Huyện")
    const district = addressParts.find(
      part =>
        part.includes("Quận") ||
        part.includes("Huyện") ||
        part.includes("Thành Phố")
    )
    if (district) districts.add(district.trim())
  })

  // Custom sort function for districts to handle "Quận 1", "Quận 2", etc. properly
  const sortDistricts = (a: string, b: string) => {
    // Extract numbers from district names for proper numerical sorting
    const getDistrictNumber = (district: string) => {
      const match = district.match(/(\d+)/)
      return match ? parseInt(match[1], 10) : 999 // Put non-numbered districts at the end
    }

    const aNum = getDistrictNumber(a)
    const bNum = getDistrictNumber(b)

    // If both have numbers, sort by number
    if (aNum !== 999 && bNum !== 999) {
      return aNum - bNum
    }

    // If only one has a number, prioritize the numbered one
    if (aNum !== 999) return -1
    if (bNum !== 999) return 1

    // If neither has numbers, sort alphabetically
    return a.localeCompare(b)
  }

  return {
    cities: Array.from(cities).sort(),
    districts: Array.from(districts).sort(sortDistricts),
  }
}

const StoreLocatorPage: React.FC = () => {
  const [selectedCity, setSelectedCity] = useState<string>("")
  const [selectedDistrict, setSelectedDistrict] = useState<string>("")
  const [searchResults, setSearchResults] = useState<Store[]>(stores)

  const { cities, districts } = useMemo(() => getLocationsFromStores(), [])

  // Filter stores based on selected criteria
  const filteredStores = useMemo(() => {
    let filtered = stores

    if (selectedCity) {
      filtered = filtered.filter(store =>
        store.address.toLowerCase().includes(selectedCity.toLowerCase())
      )
    }

    if (selectedDistrict) {
      filtered = filtered.filter(store =>
        store.address.toLowerCase().includes(selectedDistrict.toLowerCase())
      )
    }

    return filtered
  }, [selectedCity, selectedDistrict])

  const handleSearch = () => {
    setSearchResults(filteredStores)
  }

  const handleReset = () => {
    setSelectedCity("")
    setSelectedDistrict("")
    setSearchResults(stores)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50/20">
      {/* Search Form */}
      <StoreSearchForm
        cities={cities}
        districts={districts}
        selectedCity={selectedCity}
        selectedDistrict={selectedDistrict}
        onCityChange={setSelectedCity}
        onDistrictChange={setSelectedDistrict}
        onSearch={handleSearch}
        onReset={handleReset}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Store List */}
          <div className="order-2 lg:order-1 h-[550px]">
            <StoreList stores={searchResults} />
          </div>

          {/* Map */}
          <div className="order-1 lg:order-2 h-[550px]">
            <StoreMap stores={searchResults} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default StoreLocatorPage
