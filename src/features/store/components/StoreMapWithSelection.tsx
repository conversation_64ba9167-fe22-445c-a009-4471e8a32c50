import React from "react"
import { Store } from "../../../constants/stores"
import StoreMap from "./StoreMap"
import { useStoreSelection } from "../hooks/useStoreSelection"

interface StoreMapWithSelectionProps {
  stores: Store[]
  onStoreSelect?: (store: Store) => void
}

const StoreMapWithSelection: React.FC<StoreMapWithSelectionProps> = ({
  stores,
  onStoreSelect,
}) => {
  const { selectedStore, selectStore } = useStoreSelection()

  const handleStoreSelect = (store: Store) => {
    selectStore(store)
    if (onStoreSelect) {
      onStoreSelect(store)
    }
  }

  return (
    <StoreMap
      stores={stores}
      selectedStore={selectedStore}
      onStoreSelect={handleStoreSelect}
    />
  )
}

export default StoreMapWithSelection
export { type StoreMapWithSelectionProps, useStoreSelection }
