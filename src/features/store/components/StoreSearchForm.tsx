import React from "react"
import { Search, RotateCcw, ChevronDown, MapPin, Filter } from "lucide-react"
import { LiquidGlassButton } from "../../he-thong-cua-hang/shared/LiquidGlassComponents"

interface StoreSearchFormProps {
  cities: string[]
  districts: string[]
  selectedCity: string
  selectedDistrict: string
  onCityChange: (city: string) => void
  onDistrictChange: (district: string) => void
  onSearch: () => void
  onReset: () => void
}

const StoreSearchForm: React.FC<StoreSearchFormProps> = ({
  cities,
  districts,
  selectedCity,
  selectedDistrict,
  onCityChange,
  onDistrictChange,
  onSearch,
  onReset,
}) => {
  return (
    <div className="bg-gradient-to-br from-blue-50 via-white to-blue-50/30 border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-3 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full shadow-md border border-blue-100 mb-4">
            <div className="w-8 h-8 bg-[#1f4171] rounded-full flex items-center justify-center">
              <MapPin size={18} className="text-white" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">
              Tìm kiếm cửa hàng
            </h2>
          </div>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Khám phá hệ thống cửa hàng TuTiMi trên toàn TP.HCM. Chọn khu vực để
            tìm cửa hàng gần bạn nhất.
          </p>
        </div>

        {/* Search Form Card */}
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-md border border-white/50 p-6 lg:p-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-6 h-6 bg-[#1f4171]/10 rounded-lg flex items-center justify-center">
              <Filter size={14} className="text-[#1f4171]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">
              Bộ lọc tìm kiếm
            </h3>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
            {/* City/Province Selector */}
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <MapPin size={14} className="text-[#1f4171]" />
                Tỉnh, thành phố
              </label>
              <div className="relative group">
                <select
                  value={selectedCity}
                  onChange={e => onCityChange(e.target.value)}
                  className="w-full px-3 py-2.5 pr-10 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-[#1f4171]/20 focus:border-[#1f4171] appearance-none cursor-pointer transition-all duration-200 hover:border-[#1f4171]/50 text-sm"
                >
                  <option value="">Tất cả tỉnh, thành phố</option>
                  {cities.map(city => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))}
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <ChevronDown
                    size={16}
                    className="text-gray-400 group-hover:text-[#1f4171] transition-colors duration-200"
                  />
                </div>
              </div>
            </div>

            {/* District Selector */}
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <MapPin size={14} className="text-[#1f4171]" />
                Quận, huyện
              </label>
              <div className="relative group">
                <select
                  value={selectedDistrict}
                  onChange={e => onDistrictChange(e.target.value)}
                  className="w-full px-3 py-2.5 pr-10 border border-gray-300 rounded-lg bg-white text-gray-900 focus:ring-2 focus:ring-[#1f4171]/20 focus:border-[#1f4171] appearance-none cursor-pointer transition-all duration-200 hover:border-[#1f4171]/50 text-sm"
                >
                  <option value="">Tất cả quận, huyện</option>
                  {districts.map(district => (
                    <option key={district} value={district}>
                      {district}
                    </option>
                  ))}
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <ChevronDown
                    size={16}
                    className="text-gray-400 group-hover:text-[#1f4171] transition-colors duration-200"
                  />
                </div>
              </div>
            </div>

            {/* Search Button */}
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700">
                <Search size={14} className="text-[#1f4171]" />
                Tìm kiếm
              </label>
              <LiquidGlassButton
                onClick={onSearch}
                variant="primary"
                className="w-full py-2.5 px-4 text-sm"
              >
                <Search size={16} />
                TÌM KIẾM
              </LiquidGlassButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StoreSearchForm
