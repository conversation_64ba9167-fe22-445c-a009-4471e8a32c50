import React from "react"
import { MapPin, Phone, Clock, Navigation } from "lucide-react"
import { Store } from "../../../constants/stores"
import { LiquidGlassButton } from "../../he-thong-cua-hang/shared/LiquidGlassComponents"

interface StoreListProps {
  stores: Store[]
}

// Helper function to get Google Maps URL for directions
const getDirectionsUrl = (store: Store): string => {
  const encodedAddress = encodeURIComponent(store.address)
  return `https://www.google.com/maps/search/${encodedAddress}`
}

const StoreCard: React.FC<{ store: Store; index: number }> = ({
  store,
  index,
}) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-5 hover:shadow-lg hover:border-[#1f4171]/20 transition-all duration-300 group">
      {/* Store Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-[#1f4171]/10 to-[#1f4171]/20 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:from-[#1f4171]/20 group-hover:to-[#1f4171]/30 transition-all duration-300">
            <MapPin size={20} className="text-[#1f4171]" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900 group-hover:text-[#1f4171] transition-colors duration-200">
              {store.name}
            </h3>
          </div>
        </div>
      </div>

      {/* Store Details */}
      <div className="space-y-3 mb-5">
        {/* Address */}
        <div className="flex items-start gap-3">
          <div className="w-7 h-7 bg-gray-50 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5 border border-gray-200">
            <MapPin size={14} className="text-gray-600" />
          </div>
          <span className="text-sm text-gray-700 leading-relaxed font-medium">
            {store.address}
          </span>
        </div>

        {/* Phone */}
        <div className="flex items-center gap-3">
          <div className="w-7 h-7 bg-gray-50 rounded-lg flex items-center justify-center flex-shrink-0 border border-gray-200">
            <Phone size={14} className="text-gray-600" />
          </div>
          <a
            href={`tel:${store.phone}`}
            className="text-sm text-[#1f4171] hover:text-[#1f4171]/80 font-semibold transition-colors duration-200"
          >
            {store.phone}
          </a>
        </div>

        {/* Hours */}
        <div className="flex items-center gap-3">
          <div className="w-7 h-7 bg-gray-50 rounded-lg flex items-center justify-center flex-shrink-0 border border-gray-200">
            <Clock size={14} className="text-gray-600" />
          </div>
          <span className="text-sm text-gray-700 font-medium">
            Mở cửa {store.hours || "6h-23h"} hàng ngày
          </span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4 border-t border-gray-100">
        <LiquidGlassButton
          href={getDirectionsUrl(store)}
          variant="primary"
          className="flex-1 py-3 px-4 text-sm"
          aria-label={`Chỉ đường đến ${store.name}`}
        >
          <Navigation size={16} />
          Chỉ đường
        </LiquidGlassButton>
        <LiquidGlassButton
          href={`tel:${store.phone}`}
          variant="secondary"
          className="py-3 px-4"
          aria-label={`Gọi điện đến ${store.name}`}
        >
          <Phone size={16} />
        </LiquidGlassButton>
      </div>
    </div>
  )
}

const StoreList: React.FC<StoreListProps> = ({ stores }) => {
  if (stores.length === 0) {
    return (
      <div className="h-full flex flex-col">
        <div className="flex items-center justify-between mb-4 flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-[#1f4171]/10 rounded-full flex items-center justify-center">
              <MapPin size={18} className="text-[#1f4171]" />
            </div>
            <h2 className="text-xl font-bold text-gray-900">
              Danh sách cửa hàng (0)
            </h2>
          </div>
        </div>
        <div className="bg-white rounded-xl border border-gray-200 flex-1 flex items-center justify-center shadow-sm">
          <div className="text-center p-8">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin size={32} className="text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Không tìm thấy cửa hàng
            </h3>
            <p className="text-gray-600">
              Vui lòng thử lại với điều kiện tìm kiếm khác
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-[#1f4171]/10 rounded-full flex items-center justify-center">
            <MapPin size={18} className="text-[#1f4171]" />
          </div>
          <h2 className="text-xl font-bold text-gray-900">
            Danh sách cửa hàng ({stores.length})
          </h2>
        </div>
      </div>

      {/* Store List Container */}
      <div className="bg-white rounded-xl border border-gray-200 flex-1 overflow-hidden shadow-sm">
        <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          <div className="space-y-3 p-4">
            {stores.map((store, index) => (
              <StoreCard key={store.id} store={store} index={index} />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default StoreList
