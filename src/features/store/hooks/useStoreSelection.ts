import { useState } from "react"
import { Store } from "../../../constants/stores"

export const useStoreSelection = () => {
  const [selectedStore, setSelectedStore] = useState<Store | null>(null)

  const selectStore = (store: Store) => {
    setSelectedStore(store)
  }

  const clearSelection = () => {
    setSelectedStore(null)
  }

  return {
    selectedStore,
    selectStore,
    clearSelection,
  }
}
