import React from "react"
import { motion } from "framer-motion"
import { useCarousel } from "../../../hooks/useEmblaCarousel"

const ProductCategories: React.FC = () => {
  const {
    emblaRef,
    canScrollPrev,
    canScrollNext,
    scrollPrev,
    scrollNext,
    selectedIndex,
    onDotButtonClick,
  } = useCarousel({
    options: {
      loop: false,
      align: "center",
      skipSnaps: false,
    },
  })

  const categories = [
    {
      id: 1,
      title: "Trà sữa",
      image: "/web-ttmi/homepage/TTMI WEB1.jpg",
    },
    {
      id: 2,
      title: "Trà trái cây",
      image: "/web-ttmi/homepage/TTMI WEB2.jpg",
    },
    {
      id: 3,
      title: "Cà phê",
      image: "/web-ttmi/homepage/TTMI WEB3.jpg",
    },
    {
      id: 4,
      title: "Milo dầm",
      image: "/web-ttmi/homepage/TTMI WEB4.jpg",
    },
  ]

  return (
    <section className="py-12 sm:py-16 lg:py-20">
      <div className="px-6 sm:px-8 lg:px-[30px]">
        <div className="max-w-[1700px] mx-auto">
          {/* Category Headers - Desktop Grid */}
          <div className="hidden lg:grid grid-cols-4 gap-6 mb-6">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <h3 className="text-3xl font-bold" style={{ color: "#1f4171" }}>
                  {category.title}
                </h3>
              </motion.div>
            ))}
          </div>

          {/* Desktop Product Cards */}
          <div className="hidden lg:grid grid-cols-4 gap-4 xl:gap-6">
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="group cursor-default"
              >
                <div className="relative w-full aspect-[4/5] max-w-[526px] rounded-2xl overflow-hidden mx-auto">
                  {/* Background Image */}
                  <img
                    src={category.image}
                    alt={category.title}
                    className="absolute inset-0 w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />

                  {/* Hover Effect */}
                  <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Mobile Carousel */}
          <div className="lg:hidden">
            <div className="embla overflow-hidden" ref={emblaRef}>
              <div className="embla__container flex">
                {categories.map((category, index) => (
                  <div
                    key={category.id}
                    className="embla__slide flex-[0_0_100%] min-w-0 px-6 sm:px-8"
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: index * 0.2 }}
                      viewport={{ once: true }}
                      className="group cursor-default"
                    >
                      {/* Mobile Category Header */}
                      <div className="text-center mb-6 sm:mb-8">
                        <h3
                          className="text-2xl sm:text-3xl font-bold"
                          style={{ color: "#1f4171" }}
                        >
                          {category.title}
                        </h3>
                      </div>

                      {/* Mobile Product Card */}
                      <div className="relative w-full max-w-[350px] sm:max-w-[400px] h-[450px] sm:h-[500px] rounded-2xl overflow-hidden mx-auto shadow-lg">
                        <img
                          src={category.image}
                          alt={category.title}
                          className="absolute inset-0 w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                      </div>
                    </motion.div>
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile Navigation Dots */}
            <div className="flex justify-center mt-8 sm:mt-10 space-x-2">
              {categories.map((_, index) => (
                <button
                  key={index}
                  onClick={() => onDotButtonClick(index)}
                  className={`w-2 h-2 sm:w-2.5 sm:h-2.5 rounded-full transition-all duration-300 ease-in-out transform hover:scale-110 ${
                    selectedIndex === index
                      ? "opacity-100 shadow-md"
                      : "opacity-40 hover:opacity-60"
                  }`}
                  style={{
                    backgroundColor: "#1f4171",
                  }}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ProductCategories
