import React from "react"
import { motion } from "framer-motion"
import { LiquidGlassButton } from "../../he-thong-cua-hang/shared/LiquidGlassComponents"

const AboutSection: React.FC = () => {
  return (
    <section className="py-8 lg:py-12">
      <div className="mx-auto px-[30px]">
        <div className="max-w-[1350px] h-auto lg:h-[546px] mx-auto rounded-xl overflow-hidden">
          <div className="flex flex-col lg:flex-row h-full items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true, amount: 0.3 }}
              className="flex-[3] px-4 sm:px-8 py-6 sm:py-8 space-y-4 lg:space-y-6 h-full flex flex-col justify-start"
            >
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="text-2xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold mb-2"
                style={{
                  color: "#1f4171",
                }}
              >
                Về TUTIMI
              </motion.h2>

              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
                className="text-base sm:text-lg lg:text-xl font-semibold mb-4"
                style={{
                  color: "#1f4171",
                }}
              >
                TUTIMI – HÀNH TRÌNH CHINH PHỤC PHONG VỊ MỚI
              </motion.h3>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
                className="text-sm sm:text-base lg:text-lg text-gray-700 leading-relaxed mb-6"
              >
                Hành trình luôn bắt đầu từ việc chọn lựa nguyên liệu ký cẩn từ
                các vùng đất trù phú, cho đến việc bảo quản, pha chế tỉ mỉ tay
                nghề nhân. Qua những nỗ lực không ngừng, TUTIMI luôn hướng
                đến...
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                viewport={{ once: true }}
                className="pt-6"
              >
                <LiquidGlassButton
                  href="/about"
                  variant="primary"
                  className="inline-block px-8 py-3 text-lg font-medium max-w-[200px] hover:scale-100"
                  aria-label="Xem thêm thông tin về TUTIMI"
                >
                  Xem thêm
                </LiquidGlassButton>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true, amount: 0.3 }}
              className="flex-[2] relative h-full flex items-center justify-center p-4 sm:p-8"
            >
              <div className="relative rounded-2xl overflow-hidden w-full h-full max-w-[600px] max-h-[600px]">
                <img
                  src="/web-ttmi/homepage/TTMI-WEB.jpg"
                  alt="Về KATINAT - Đội ngũ nhân viên chuyên nghiệp"
                  className="w-full h-full object-cover"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutSection
