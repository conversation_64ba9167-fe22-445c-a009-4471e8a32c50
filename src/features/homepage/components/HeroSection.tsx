import React, { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useCarousel } from "../../../hooks/useEmblaCarousel"

interface HeroSlide {
  id: number
  backgroundImage: string
  backgroundColor: string
}

const HeroSection: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  useEffect(() => {
    const checkMobileMenu = () => {
      const mobileMenu = document.querySelector(".lg\\:hidden.fixed.inset-0")
      const isOpen = mobileMenu && !mobileMenu.classList.contains("invisible")
      setIsMobileMenuOpen(!!isOpen)
    }

    checkMobileMenu()

    const observer = new MutationObserver(checkMobileMenu)
    const targetNode = document.body

    if (targetNode) {
      observer.observe(targetNode, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["class"],
      })
    }

    return () => observer.disconnect()
  }, [])

  const heroSlides: HeroSlide[] = [
    {
      id: 1,
      backgroundImage: "/web-ttmi/homepage/TTMI-COVER-WEB-MATCHA.jpg",
      backgroundColor: "from-amber-800 via-amber-700 to-amber-900",
    },
    {
      id: 2,
      backgroundImage: "/web-ttmi/homepage/TTMI-COVER-WEB-MILO.jpg",
      backgroundColor: "from-orange-800 via-orange-700 to-orange-900",
    },
    {
      id: 3,
      backgroundImage: "/web-ttmi/homepage/ttmi-cover-web-ck.jpg",
      backgroundColor: "from-green-800 via-green-700 to-green-900",
    },
  ]

  const {
    emblaRef,
    canScrollPrev,
    canScrollNext,
    scrollPrev,
    scrollNext,
    selectedIndex,
    scrollSnaps,
    onDotButtonClick,
  } = useCarousel({
    options: {
      align: "center",
      loop: true,
      skipSnaps: false,
      dragFree: false,
      containScroll: "trimSnaps",
    },
    autoplay: true,
    autoplayDelay: 5000,
  })

  return (
    <div className="px-[5px] sm:px-[15px] lg:px-[20px]">
      <section className="relative w-full overflow-hidden rounded-xl">
        <div className="embla h-full overflow-hidden rounded-xl" ref={emblaRef}>
          <div className="embla__container h-full">
            {heroSlides.map((slide, index) => (
              <div key={slide.id} className="embla__slide relative h-full">
                <div className="absolute inset-0 bg-white"></div>

                <motion.div
                  initial={{ opacity: 0, scale: 1.1 }}
                  animate={
                    selectedIndex === index
                      ? { opacity: 1, scale: 1 }
                      : { opacity: 0, scale: 1.1 }
                  }
                  transition={{ duration: 1.2, delay: 0.2 }}
                  className="relative z-10 w-full h-full"
                >
                  {/* Hero Image */}
                  <img
                    src={slide.backgroundImage}
                    alt={`Hero slide ${slide.id}`}
                    className="w-full h-auto object-contain object-center sm:object-cover sm:h-[300px] md:h-[400px] lg:h-auto lg:object-contain"
                  />
                </motion.div>
              </div>
            ))}
          </div>
        </div>

        <button
          onClick={scrollPrev}
          disabled={!canScrollPrev}
          className={`absolute left-4 sm:left-6 lg:left-8 top-1/2 -translate-y-1/2 z-20 w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 hover:border-white/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${
            isMobileMenuOpen ? "opacity-0 pointer-events-none" : "opacity-100"
          }`}
          aria-label="Previous slide"
        >
          <ChevronLeft size={10} className="sm:w-3 sm:h-3 lg:w-4 lg:h-4" />
        </button>

        <button
          onClick={scrollNext}
          disabled={!canScrollNext}
          className={`absolute right-4 sm:right-6 lg:right-8 top-1/2 -translate-y-1/2 z-20 w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 hover:border-white/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed ${
            isMobileMenuOpen ? "opacity-0 pointer-events-none" : "opacity-100"
          }`}
          aria-label="Next slide"
        >
          <ChevronRight size={10} className="sm:w-3 sm:h-3 lg:w-4 lg:h-4" />
        </button>

        <div
          className={`absolute bottom-3 sm:bottom-4 lg:bottom-8 left-1/2 -translate-x-1/2 z-20 flex space-x-1.5 sm:space-x-2 lg:space-x-3 transition-all duration-300 ${
            isMobileMenuOpen ? "opacity-0 pointer-events-none" : "opacity-100"
          }`}
        >
          {scrollSnaps.map((_, index) => (
            <button
              key={index}
              onClick={() => onDotButtonClick(index)}
              className={`w-1.5 h-1.5 sm:w-2 sm:h-2 lg:w-3 lg:h-3 rounded-full transition-all duration-300 ${
                index === selectedIndex
                  ? "bg-white scale-125"
                  : "bg-white/40 hover:bg-white/60"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </section>
    </div>
  )
}

export default HeroSection
