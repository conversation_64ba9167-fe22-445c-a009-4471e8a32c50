import React from "react"
import { motion } from "framer-motion"
import { Smartphone, ShoppingBag, Clock, Star } from "lucide-react"

const Partner: React.FC = (): JSX.Element => {
  const deliveryApps = [
    {
      name: "Grab Food",
      description: "Đặt hàng nhanh chóng với Grab Food",
      rating: "4.8",
      deliveryTime: "15-25 phút",
      link: "https://food.grab.com/vn/vi/",
      color: "#00B14F",
      logo: "/logos/grabfood.png",
      features: ["Giao hàng nhanh", "<PERSON><PERSON><PERSON><PERSON> khuyến mãi", "Thanh toán đa dạng"],
    },
    {
      name: "Shopee Food",
      description: "Thưởng thức TuTiMi qua Shopee Food",
      rating: "4.7",
      deliveryTime: "20-30 phút",
      link: "https://shopeefood.vn/",
      color: "#EE4D2D",
      logo: "/logos/shopeefoodvn.png",
      features: ["Freeship 0đ", "Voucher hấp dẫn", "<PERSON><PERSON><PERSON> điểm thưởng"],
    },
    {
      name: "BeFood",
      description: "Đặt món yêu thích từ TuTiMi trên BeFood",
      rating: "4.6",
      deliveryTime: "18-28 phút",
      link: "https://food.be.com.vn/",
      color: "#3AC5C9",
      logo: "/logos/befood.png",
      features: ["Giao hàng 24/7", "Combo tiết kiệm", "Đánh giá cao"],
    },
    {
      name: "Xanh SM",
      description: "Đặt hàng TuTiMi tiện lợi với Xanh SM",
      rating: "4.5",
      deliveryTime: "20-30 phút",
      link: "https://xanhsm.com/",
      color: "#00B050",
      logo: "/logos/xanh-sm.png",
      features: ["Thân thiện môi trường", "Dịch vụ tận tâm"],
    },
  ]

  const orderingBenefits = [
    {
      icon: Clock,
      title: "Giao Hàng Nhanh",
      description: "Thời gian giao hàng từ 15-35 phút",
    },
    {
      icon: Star,
      title: "Chất Lượng Đảm Bảo",
      description: "Đánh giá 4.5+ sao trên tất cả platforms",
    },
    {
      icon: ShoppingBag,
      title: "Đa Dạng Ưu Đãi",
      description: "Voucher, freeship và combo tiết kiệm",
    },
    {
      icon: Smartphone,
      title: "Đặt Hàng Dễ Dàng",
      description: "Giao diện thân thiện, thanh toán đa dạng",
    },
  ]

  return (
    <div className="min-h-screen bg-white">
      <div className="py-8 lg:py-12">
        {/* Header Section */}
        <section className="py-8 lg:py-12">
          <div className="px-[30px]">
            <div className="max-w-[1350px] mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h1
                  className="text-4xl lg:text-6xl font-bold mb-4"
                  style={{ color: "#1f4171" }}
                >
                  ĐỐI TÁC
                </h1>
                <h2
                  className="text-xl lg:text-2xl font-semibold mb-6"
                  style={{ color: "#1f4171" }}
                >
                  TUTIMI – ĐẶT HÀNG ONLINE
                </h2>
                <p className="text-base lg:text-lg text-gray-700 leading-relaxed max-w-3xl mx-auto">
                  Thưởng thức TuTiMi mọi lúc mọi nơi với các ứng dụng đặt hàng
                  online. Giao hàng nhanh chóng, ưu đãi hấp dẫn và chất lượng
                  đảm bảo.
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Delivery Apps Section */}
        <section className="py-8 lg:py-12">
          <div className="px-[30px]">
            <div className="max-w-[1350px] mx-auto">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                {deliveryApps.map((app, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group"
                  >
                    <a
                      href={app.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-8 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100 h-[350px] w-full flex flex-col overflow-hidden"
                    >
                      <div className="text-center flex-1 flex flex-col">
                        {/* App Logo */}
                        <div className="w-20 h-20 mx-auto mb-6 rounded-xl overflow-hidden bg-white shadow-md flex items-center justify-center p-2">
                          <img
                            src={app.logo}
                            alt={`${app.name} logo`}
                            className="w-full h-full object-contain rounded-lg"
                          />
                        </div>

                        {/* App Info */}
                        <h3
                          className="text-2xl font-bold mb-2"
                          style={{ color: "#1f4171" }}
                        >
                          {app.name}
                        </h3>
                        <p className="text-gray-600 mb-4 flex-1 text-sm">
                          {app.description}
                        </p>

                        {/* Features */}
                        <div className="mt-auto pb-4">
                          <div className="flex flex-wrap gap-2 justify-center">
                            {app.features.map((feature, idx) => (
                              <div
                                key={idx}
                                className="text-xs text-gray-600 bg-gray-50 px-3 py-2 rounded-full whitespace-nowrap"
                              >
                                {feature}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </a>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-8 lg:py-12">
          <div className="px-[30px]">
            <div className="max-w-[1350px] mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h3
                  className="text-2xl lg:text-3xl font-bold mb-4"
                  style={{ color: "#1f4171" }}
                >
                  Tại Sao Chọn Đặt Hàng Online?
                </h3>
                <p className="text-base lg:text-lg text-gray-700">
                  Những lợi ích khi đặt hàng TuTiMi qua các ứng dụng
                </p>
              </motion.div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {orderingBenefits.map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="text-center p-6 bg-white rounded-2xl shadow-lg border border-gray-100 h-[280px] w-full flex flex-col overflow-hidden"
                  >
                    <div
                      className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: "#1f4171" }}
                    >
                      <benefit.icon size={28} className="text-white" />
                    </div>
                    <h4
                      className="text-lg font-bold mb-2"
                      style={{ color: "#1f4171" }}
                    >
                      {benefit.title}
                    </h4>
                    <p className="text-gray-700 text-sm lg:text-base leading-relaxed flex-1 pb-4">
                      {benefit.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}

export default Partner
