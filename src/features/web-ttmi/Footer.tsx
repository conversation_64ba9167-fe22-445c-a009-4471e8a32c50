import React from "react"
import { Mail, MapPin, Phone } from "lucide-react"
import { FacebookIcon, TiktokIcon } from "../../components/icons"

interface TutimiFooterProps {
  className?: string
}

/**
 * TUTIMI Footer Component
 * Replicates the exact layout and styling from the original TUTIMI footer
 * with 100% visual fidelity using Tailwind CSS
 */
const socialLinks = [
  {
    href: "https://www.facebook.com/trasuatutimi",
    label: "Theo dõi TUTIMI trên Facebook",
    icon: FacebookIcon,
  },
  {
    href: "https://www.tiktok.com/@trasua.tutimi?_t=8l6JjVpehR1&_r=1",
    label: "Theo dõi TUTIMI trên TikTok",
    icon: TiktokIcon,
  },
]

const contactInfo = [
  {
    icon: Mail,
    text: "Email: <EMAIL>",
  },
  {
    icon: MapPin,
    text: "Office: 39/50 Đặng Thù<PERSON>, P.13, <PERSON><PERSON>, TP.HCM",
  },
  {
    icon: Phone,
    text: "Hotline: 0901837263",
  },
]

const policyLinks = [
  {
    href: "#",
    text: "– <PERSON><PERSON> chế hoạt động và Chính sách bảo mật.",
  },
  {
    href: "#",
    text: "– Chính sách vận chuyển.",
  },
  {
    href: "#",
    text: "– Chính sách thanh toán.",
  },
]

const TutimiFooter: React.FC<TutimiFooterProps> = ({ className = "" }) => {
  return (
    <div className={className}>
      {/* Main Footer */}
      <footer
        className="relative pt-8 pb-4 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('/web-ttmi/homepage/background-footer.jpg')",
        }}
        role="contentinfo"
        itemScope
        itemType="https://schema.org/WPFooter"
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/50"></div>
        <div className="relative max-w-[1380px] mx-auto px-4 z-10">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-4">
            {/* Column 1: Company Info & Social Media */}
            <div className="lg:px-4 text-left lg:text-left">
              <div className="mb-6">
                <div className="mb-4">
                  <img
                    loading="lazy"
                    decoding="async"
                    className="mx-auto lg:mx-0 h-auto w-auto max-w-[200px] lg:max-w-[300px] object-contain"
                    src="/logos/tutimi-text-white.png"
                    alt="TUTIMI Logo"
                    width="300"
                    height="104"
                  />
                </div>

                <h4 className="text-white font-bold uppercase mb-4 text-lg">
                  VỀ TUTIMI
                </h4>
                <p className="text-white leading-relaxed mb-6 text-sm text-left">
                  TUTIMI – HÀNH TRÌNH CHINH PHỤC PHONG VỊ MỚI
                  <br />
                  TUTIMI không ngừng theo đuổi sứ mệnh mang phong vị mới từ
                  những vùng đất trứ danh tại Việt Nam và trên thế giới đến
                  khách hàng.
                </p>
              </div>

              {/* Social Media */}
              <div className="flex justify-center lg:justify-start gap-4">
                {socialLinks.map((link, index) => (
                  <a
                    key={index}
                    href={link.href}
                    className="text-white hover:text-gray-300 transition-colors duration-300"
                    aria-label={link.label}
                  >
                    <link.icon size={24} />
                  </a>
                ))}
              </div>
            </div>

            {/* Column 2: Contact Info & Policies */}
            <div className="lg:px-4 mt-6 lg:mt-8">
              <div className="mb-6">
                <h4 className="text-white font-bold uppercase mb-4 text-lg text-left lg:text-left">
                  LIÊN HỆ
                </h4>
                <ul className="space-y-3 text-white text-sm text-start">
                  {contactInfo.map((item, index) => (
                    <li
                      key={index}
                      className="flex items-start justify-start gap-2"
                    >
                      <item.icon
                        size={17}
                        className="text-white mt-0.5 flex-shrink-0"
                      />
                      <span>{item.text}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="text-left lg:text-left">
                <h4 className="text-white font-bold uppercase mb-4 text-lg">
                  HỖ TRỢ VÀ CHÍNH SÁCH
                </h4>
                <ul className="space-y-2 text-sm text-left">
                  {policyLinks.map((link, index) => (
                    <li key={index}>
                      <a
                        href={link.href}
                        className="text-white hover:text-gray-300 hover:underline transition-colors duration-300"
                      >
                        {link.text}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Column 3: TuTiMi Footer Image */}
            <div className="lg:px-4 flex items-center justify-center">
              <img
                src="/web-ttmi/homepage/tutimi-footer.png"
                className="max-w-[848px] max-h-[294px] w-full h-auto object-contain rounded-lg"
                alt="TuTiMi Footer"
                decoding="async"
                loading="lazy"
              />
            </div>
          </div>
        </div>

        {/* Copyright Section - Full Width Border */}
        <div className="mt-8 pt-6 border-t border-white/20 w-full">
          <div className="max-w-[1380px] mx-auto px-4">
            <div className="text-center">
              <p className="text-white/80 text-sm italic font-sans">
                ©TuTiMi 2025 | All rights reserved. Website by TuTiMi.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default TutimiFooter
