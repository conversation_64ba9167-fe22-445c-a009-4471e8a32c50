import React, { useState, useEffect } from "react"

const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const [currentPath, setCurrentPath] = useState("")

  useEffect(() => {
    setCurrentPath(window.location.pathname)
  }, [])

  const menuItems = [
    { title: "Trang chủ", href: "/" },
    { title: "Về chúng tôi", href: "/about" },
    { title: "<PERSON>u", href: "/menu" },
    { title: "<PERSON><PERSON><PERSON> hàng", href: "/cua-hang" },
    { title: "<PERSON><PERSON><PERSON> tác", href: "/partner" },
    { title: "<PERSON><PERSON><PERSON> hệ", href: "/contact" },
  ].map(item => ({
    ...item,
    active: item.href === currentPath,
  }))

  return (
    <header
      id="header"
      className="site-header header-layout-primary-menu header-layout-secondary-none relative z-50"
      role="banner"
    >
      {/* Header Primary - With background image */}
      <div
        id="header-primary"
        className="header-part header-primary header-primary-menu text-white relative"
        style={{
          backgroundImage: "url(/web-ttmi/homepage/background-footer.jpg)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="max-w-[1380px] mx-auto px-4 relative z-10">
          <div className="flex items-center justify-between min-h-[80px] md:min-h-[100px] lg:min-h-[286px]">
            {/* Branding - Logo Section */}
            <div
              id="branding"
              className="site-branding branding flex items-center"
            >
              <div id="site-logo" className="site-logo-mixedcustom">
                <div className="site-logo-mixed-image">
                  <a href="/" className="custom-logo-link" rel="home">
                    <img
                      src="/logos/tutimi-text-white.png"
                      className="custom-logo max-w-[160px] h-[80px] sm:max-w-[180px] sm:h-[100px] md:max-w-[200px] md:h-[110px] lg:max-w-[240px] lg:h-[144px] xl:max-w-[280px] xl:h-[168px] 2xl:max-w-[360px] 2xl:h-[216px] object-contain drop-shadow-lg transition-transform duration-300"
                      alt="TuTiMi"
                    />
                  </a>
                </div>
              </div>
            </div>

            {/* Header Aside - Navigation */}
            <div id="header-aside" className="header-aside flex items-center">
              {/* Screen Reader Text */}
              <div className="sr-only">Primary Navigation Menu</div>

              {/* Primary Navigation */}
              <nav
                id="menu-primary"
                className="menu nav-menu menu-primary hidden lg:block"
                role="navigation"
              >
                <ul className="menu-items flex items-center space-x-0">
                  {menuItems.map((item, index) => (
                    <li
                      key={index}
                      className={`menu-item ${
                        item.active ? "current-menu-item" : ""
                      }`}
                    >
                      <a
                        href={item.href}
                        className={`block px-4 py-3 font-medium transition-all duration-300 ease-in-out ${
                          item.active
                            ? "bg-white text-primary-navy"
                            : "text-white border-transparent hover:bg-white hover:text-primary-navy"
                        }`}
                        target={
                          item.href.startsWith("http") ? "_blank" : "_self"
                        }
                        rel={
                          item.href.startsWith("http")
                            ? "noopener noreferrer"
                            : undefined
                        }
                        aria-current={item.active ? "page" : undefined}
                      >
                        <span className="menu-title tracking-tight uppercase">
                          {item.title}
                        </span>
                      </a>
                    </li>
                  ))}
                </ul>
              </nav>

              {/* Mobile Menu Toggle */}
              <button
                className="menu-toggle lg:hidden flex items-center px-3 py-2 text-white bg-[#1f4171] bg-opacity-80 border border-white border-opacity-60 rounded-md hover:bg-[#2a5490] hover:bg-opacity-90 transition-all duration-300 backdrop-blur-sm"
                onClick={e => {
                  e.preventDefault()
                  setIsMobileMenuOpen(!isMobileMenuOpen)
                }}
                aria-label="Toggle mobile menu"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={
                      isMobileMenuOpen
                        ? "M6 18L18 6M6 6l12 12"
                        : "M4 6h16M4 12h16M4 18h16"
                    }
                  />
                </svg>
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <div
            className={`lg:hidden fixed inset-0 bg-primary-navy z-[100] overflow-y-auto transition-all duration-300 ease-in-out ${
              isMobileMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"
            }`}
          >
            <div
              className={`min-h-screen flex flex-col transition-transform duration-300 ease-in-out ${
                isMobileMenuOpen ? "translate-y-0" : "-translate-y-full"
              }`}
            >
              {/* Header with close button */}
              <div className="flex items-center justify-between p-6 border-b border-white/20">
                <h2 className="text-white font-medium text-lg uppercase tracking-wide">
                  Menu
                </h2>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="text-white p-2 hover:bg-white/20 rounded-md transition-colors"
                  aria-label="Close menu"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* Menu items */}
              <div className="flex-1 py-8">
                <ul className="menu-items">
                  {menuItems.map((item, index) => (
                    <li
                      key={index}
                      className={`menu-item transition-all duration-300 ease-out ${
                        isMobileMenuOpen
                          ? "opacity-100 translate-x-0"
                          : "opacity-0 -translate-x-8"
                      }`}
                      style={{
                        transitionDelay: isMobileMenuOpen
                          ? `${index * 50}ms`
                          : "0ms",
                      }}
                    >
                      <a
                        href={item.href}
                        className={`block px-6 py-5 font-medium text-base uppercase tracking-wide transition-all duration-300 ease-in-out ${
                          item.active
                            ? "bg-white text-primary-navy border-l-4 border-primary-navy"
                            : "text-white"
                        }`}
                        target={
                          item.href.startsWith("http") ? "_blank" : "_self"
                        }
                        rel={
                          item.href.startsWith("http")
                            ? "noopener noreferrer"
                            : undefined
                        }
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <span className="menu-title">{item.title}</span>
                      </a>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Footer */}
              <div className="p-6 border-t border-white/20">
                <div className="text-center text-white/80 text-sm">
                  © 2025 TuTiMi
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
