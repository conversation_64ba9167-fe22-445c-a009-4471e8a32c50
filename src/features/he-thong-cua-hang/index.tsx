import React from "react"
import <PERSON> from "./components/Hero"
import BrandStory from "./components/BrandStory"
import Menu from "./components/Menu"
import StoreLocator from "./components/StoreLocator"
import Feedback from "./components/Feedback"
import AppIntegration from "./components/AppIntegration"
import CustomerSupport from "./components/CustomerSupport"
import FacebookIntegration from "./components/FacebookIntegration"
import Footer from "./components/Footer"

const HeThongCuaHangPage: React.FC = (): JSX.Element => {
  return (
    <div className="bg-white">
      <div className="-mx-4 sm:mx-0">
        <Hero />
      </div>
      <StoreLocator />
      <BrandStory />
      <Menu />
      <Feedback />
      <AppIntegration />
      <CustomerSupport />
      <FacebookIntegration />
      <Footer />
    </div>
  )
}

export default HeThongCuaHangPage
