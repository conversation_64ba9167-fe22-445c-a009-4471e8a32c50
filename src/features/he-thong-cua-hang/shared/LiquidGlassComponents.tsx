import React from "react"

// Liquid Glass Components following Apple's design system

export interface LiquidGlassCardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
}

export const LiquidGlassCard: React.FC<LiquidGlassCardProps> = ({
  children,
  className = "",
  hover = true,
}) => (
  <div
    className={`
      relative bg-white/[0.08] backdrop-blur-2xl
      border border-white/[0.12] rounded-3xl
      shadow-[0_8px_32px_rgba(0,0,0,0.12)]
      ${
        hover
          ? "hover:bg-white/[0.12] hover:border-white/[0.18] hover:shadow-[0_12px_48px_rgba(0,0,0,0.18)]"
          : ""
      }
      transition-all duration-500 ease-out
      ${className}
    `}
  >
    <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/[0.05] to-transparent pointer-events-none" />
    {children}
  </div>
)

export interface LiquidGlassButtonProps {
  children: React.ReactNode
  onClick?: () => void
  href?: string
  className?: string
  variant?: "primary" | "secondary" | "glass"
  "aria-label"?: string
  disabled?: boolean
  target?: "_blank" | "_self" | "_parent" | "_top"
}

export const LiquidGlassButton: React.FC<LiquidGlassButtonProps> = ({
  children,
  onClick,
  href,
  className = "",
  variant = "glass",
  "aria-label": ariaLabel,
  disabled = false,
  target,
}) => {
  const Component = href ? "a" : "button"

  const variants = {
    primary: `
      bg-gradient-to-r from-primary-navy to-blue-600
      text-white border-0 shadow-[0_4px_20px_rgba(31,65,113,0.4)]
      hover:shadow-[0_8px_32px_rgba(31,65,113,0.6)] hover:scale-105
    `,
    secondary: `
      bg-white/[0.08] backdrop-blur-xl border border-white/[0.15]
      text-primary-navy hover:bg-white/[0.15] hover:border-white/[0.25]
      shadow-[0_4px_20px_rgba(31,65,113,0.15)]
    `,
    glass: `
      bg-white/[0.06] backdrop-blur-xl border border-white/[0.12]
      text-white hover:bg-white/[0.12] hover:border-white/[0.2]
      shadow-[0_4px_20px_rgba(255,255,255,0.1)]
    `,
  }

  return (
    <Component
      href={href}
      onClick={onClick}
      target={href ? target : undefined}
      rel={href && target === "_blank" ? "noopener noreferrer" : undefined}
      aria-label={ariaLabel}
      disabled={!href ? disabled : undefined}
      className={`
        group relative px-8 py-4 rounded-2xl font-semibold
        transition-all duration-300 ease-out
        hover:scale-105 active:scale-95
        focus:outline-none focus:ring-2 focus:ring-primary-navy/50
        flex items-center justify-center
        ${disabled ? "opacity-50 cursor-not-allowed" : ""}
        ${variants[variant]}
        ${className}
      `}
    >
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-white/[0.05] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      <span className="relative flex items-center justify-center gap-2">
        {children}
      </span>
    </Component>
  )
}

export interface SectionProps {
  children: React.ReactNode
  className?: string
  id?: string
}

export const Section: React.FC<SectionProps> = ({
  children,
  className = "",
  id,
}) => (
  <section id={id} className={`py-12 sm:py-20 md:py-24 px-4 ${className}`}>
    {children}
  </section>
)

export interface ContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "6xl" | "7xl"
}

export const Container: React.FC<ContainerProps> = ({
  children,
  className = "",
  maxWidth = "6xl",
}) => {
  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    "2xl": "max-w-2xl",
    "6xl": "max-w-6xl",
    "7xl": "max-w-7xl",
  }

  return (
    <div className={`${maxWidthClasses[maxWidth]} mx-auto ${className}`}>
      {children}
    </div>
  )
}
