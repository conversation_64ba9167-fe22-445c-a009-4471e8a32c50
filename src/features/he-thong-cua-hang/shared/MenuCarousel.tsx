import React from "react"
import { motion } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useCarousel } from "../../../hooks/useEmblaCarousel"
import { LiquidGlassCard } from "./LiquidGlassComponents"

interface MenuItem {
  id: number
  name: string
  image: string
  description?: string
}

interface MenuCarouselProps {
  menuItems: MenuItem[]
  className?: string
  showDots?: boolean
  autoplay?: boolean
  autoplayDelay?: number
}

/**
 * Menu-specific carousel component for mobile devices
 * Shows individual menu items in a swipeable format
 */
const MenuCarousel: React.FC<MenuCarouselProps> = ({
  menuItems,
  className = "",
  showDots = true,
  autoplay = true,
  autoplayDelay = 5000,
}) => {
  const {
    emblaRef,
    canScrollPrev,
    canScrollNext,
    scrollPrev,
    scrollNext,
    selectedIndex,
    scrollSnaps,
    onDotButtonClick,
  } = useCarousel({
    options: {
      align: "center",
      loop: true,
      skipSnaps: false,
      dragFree: false,
      containScroll: "trimSnaps",
    },
    autoplay,
    autoplayDelay,
  })

  return (
    <div className={`relative ${className}`}>
      {/* Mobile/Tablet Carousel View */}
      <div className="block lg:hidden">
        <div className="embla overflow-hidden touch-optimized" ref={emblaRef}>
          <div className="embla__container flex touch-pan-y touch-pinch-zoom">
            {menuItems.map((item, index) => (
              <div
                key={item.id}
                className="embla__slide flex-[0_0_85%] sm:flex-[0_0_70%] min-w-0"
                style={{
                  transform: "translate3d(0, 0, 0)",
                }}
              >
                <LiquidGlassCard className="h-full">
                  <motion.div
                    className="relative group overflow-hidden rounded-xl sm:rounded-2xl h-[400px] sm:h-[450px]"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.3 }}
                  >
                    {/* Menu Item Image */}
                    <div className="relative h-3/4 overflow-hidden rounded-t-xl sm:rounded-t-2xl">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        loading="lazy"
                      />

                      {/* Gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

                      {/* Glass overlay effects */}
                      <div className="absolute inset-0 bg-gradient-to-br from-white/15 via-white/5 to-transparent mix-blend-overlay" />
                      <div className="absolute inset-0 bg-gradient-to-tl from-transparent via-white/8 to-white/12 mix-blend-soft-light" />
                    </div>

                    {/* Menu Item Info */}
                    <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
                      <div className="relative rounded-2xl border border-white/30 bg-white/15 p-4 shadow-xl backdrop-blur-xl transition-all duration-300 hover:bg-white/20">
                        {/* Glass layers for info card */}
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 via-white/5 to-transparent" />
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-tl from-transparent via-white/10 to-white/15 opacity-60" />

                        {/* Glass highlight */}
                        <div className="absolute inset-x-0 top-0 h-px rounded-t-2xl bg-gradient-to-r from-transparent via-white/50 to-transparent" />

                        <div className="relative">
                          <h3 className="text-lg sm:text-xl font-bold text-white drop-shadow-lg mb-2">
                            {item.name}
                          </h3>
                          {item.description && (
                            <p className="text-sm text-white/90 drop-shadow">
                              {item.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </LiquidGlassCard>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Arrows */}
        <motion.button
          onClick={scrollPrev}
          disabled={!canScrollPrev}
          className={`absolute left-2 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center transition-all duration-300 ${
            canScrollPrev
              ? "opacity-100 hover:bg-white/20 hover:scale-110"
              : "opacity-50 cursor-not-allowed"
          }`}
          whileHover={{ scale: canScrollPrev ? 1.1 : 1 }}
          whileTap={{ scale: canScrollPrev ? 0.95 : 1 }}
        >
          <ChevronLeft size={24} className="text-primary-navy" />
        </motion.button>

        <motion.button
          onClick={scrollNext}
          disabled={!canScrollNext}
          className={`absolute right-2 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center transition-all duration-300 ${
            canScrollNext
              ? "opacity-100 hover:bg-white/20 hover:scale-110"
              : "opacity-50 cursor-not-allowed"
          }`}
          whileHover={{ scale: canScrollNext ? 1.1 : 1 }}
          whileTap={{ scale: canScrollNext ? 0.95 : 1 }}
        >
          <ChevronRight size={24} className="text-primary-navy" />
        </motion.button>

        {/* Dot Indicators */}
        {showDots && scrollSnaps.length > 1 && (
          <div className="flex justify-center mt-8 gap-3">
            {scrollSnaps.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => onDotButtonClick(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === selectedIndex
                    ? "bg-primary-navy scale-125 shadow-lg"
                    : "bg-primary-navy/30 hover:bg-primary-navy/50"
                }`}
                whileHover={{ scale: index === selectedIndex ? 1.25 : 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label={`Go to menu item ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Desktop Grid View - Shows original menu image */}
      <div className="hidden lg:block">
        <LiquidGlassCard className="p-4 sm:p-6 md:p-8 max-w-4xl w-full mx-auto">
          <div className="relative">
            <img
              src="/images/menu.jpg"
              alt="Thực đơn TuTiMi - Menu đồ uống đa dạng"
              className="w-full h-auto rounded-lg sm:rounded-xl shadow-2xl"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent rounded-lg sm:rounded-xl pointer-events-none" />
          </div>
        </LiquidGlassCard>
      </div>
    </div>
  )
}

export default MenuCarousel
