import React from "react"
import { motion } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useCarousel } from "../../../hooks/useEmblaCarousel"

interface MobileCarouselProps {
  children: React.ReactNode[]
  className?: string
  showDots?: boolean
  showArrows?: boolean
  autoplay?: boolean
  autoplayDelay?: number
  slidesToShow?: {
    mobile: number
    tablet: number
    desktop: number
  }
}

/**
 * Mobile-first carousel component with TTMI liquid glass design
 * Only shows carousel on mobile/tablet, reverts to grid on desktop
 */
const MobileCarousel: React.FC<MobileCarouselProps> = ({
  children,
  className = "",
  showDots = true,
  showArrows = false,
  autoplay = true,
  autoplayDelay = 4000,
  slidesToShow = { mobile: 1, tablet: 2, desktop: 3 },
}) => {
  const {
    emblaRef,
    canScrollPrev,
    canScrollNext,
    scrollPrev,
    scrollNext,
    selectedIndex,
    scrollSnaps,
    onDotButtonClick,
  } = useCarousel({
    options: {
      align: "center",
      loop: true,
      skipSnaps: false,
      dragFree: false,
      containScroll: "trimSnaps",
    },
    autoplay,
    autoplayDelay,
  })

  return (
    <div className={`relative ${className}`}>
      {/* Mobile/Tablet Carousel View */}
      <div className="block lg:hidden">
        <div className="embla overflow-hidden touch-optimized" ref={emblaRef}>
          <div className="embla__container flex touch-pan-y touch-pinch-zoom">
            {children.map((child, index) => (
              <div
                key={index}
                className="embla__slide flex-[0_0_100%] sm:flex-[0_0_50%] min-w-0 px-2"
                style={{
                  transform: "translate3d(0, 0, 0)",
                }}
              >
                {child}
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Arrows - Only show if enabled */}
        {showArrows && (
          <>
            <motion.button
              onClick={scrollPrev}
              disabled={!canScrollPrev}
              className={`absolute left-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center transition-all duration-300 ${
                canScrollPrev
                  ? "opacity-100 hover:bg-white/20 hover:scale-110"
                  : "opacity-50 cursor-not-allowed"
              }`}
              whileHover={{ scale: canScrollPrev ? 1.1 : 1 }}
              whileTap={{ scale: canScrollPrev ? 0.95 : 1 }}
            >
              <ChevronLeft size={20} className="text-primary-navy" />
            </motion.button>

            <motion.button
              onClick={scrollNext}
              disabled={!canScrollNext}
              className={`absolute right-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white/10 backdrop-blur-xl border border-white/20 flex items-center justify-center transition-all duration-300 ${
                canScrollNext
                  ? "opacity-100 hover:bg-white/20 hover:scale-110"
                  : "opacity-50 cursor-not-allowed"
              }`}
              whileHover={{ scale: canScrollNext ? 1.1 : 1 }}
              whileTap={{ scale: canScrollNext ? 0.95 : 1 }}
            >
              <ChevronRight size={20} className="text-primary-navy" />
            </motion.button>
          </>
        )}

        {/* Dot Indicators - Only show if enabled and there are multiple slides */}
        {showDots && scrollSnaps.length > 1 && (
          <div className="flex justify-center mt-6 gap-2">
            {scrollSnaps.map((_, index) => (
              <motion.button
                key={index}
                onClick={() => onDotButtonClick(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === selectedIndex
                    ? "bg-primary-navy scale-125"
                    : "bg-primary-navy/30 hover:bg-primary-navy/50"
                }`}
                whileHover={{ scale: index === selectedIndex ? 1.25 : 1.1 }}
                whileTap={{ scale: 0.9 }}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Desktop Grid View - Maintains original layout */}
      <div className="hidden lg:block">
        <div
          className={`grid gap-3 sm:gap-4 lg:gap-6 ${
            slidesToShow.desktop === 4
              ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4"
              : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
          }`}
        >
          {children}
        </div>
      </div>
    </div>
  )
}

export default MobileCarousel
