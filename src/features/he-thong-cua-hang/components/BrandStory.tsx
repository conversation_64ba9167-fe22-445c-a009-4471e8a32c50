import React from "react"
import { motion } from "framer-motion"
import { Section, Container } from "../shared/LiquidGlassComponents"
import { useScrollAnimation } from "../../../hooks/useScrollAnimation"
import MobileCarousel from "../shared/MobileCarousel"

interface BrandStoryProps {
  className?: string
}

interface GalleryItem {
  src: string
  title: string
  alt: string
}

/**
 * Brand Story Section Component
 * Showcases TuTiMi's history, commitment, and brand values
 */
const BrandStory: React.FC<BrandStoryProps> = ({ className = "" }) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })

  const galleryItems: GalleryItem[] = [
    {
      src: "/images/trasua.png",
      title: "Trà Sữa",
      alt: "Trà sữa thơm ngon đặc trưng TuTiMi",
    },
    {
      src: "/images/trasuamatcha.png",
      title: "Trà Sữa Matcha",
      alt: "Trà sữa matcha cao cấp hương vị đậm đà",
    },
    {
      src: "/images/tratraicay.png",
      title: "Trà Trái Cây",
      alt: "Trà trái cây tươi mát giải khát",
    },
    {
      src: "/images/milodam.png",
      title: "Milo Dầm",
      alt: "Milo dầm thập cẩm đặc biệt",
    },
  ]

  return (
    <Section className={className} id="brand-story">
      <Container>
        <motion.div
          ref={ref}
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-sans font-bold text-primary-navy mb-4 sm:mb-6">
            Câu chuyện thương hiệu
          </h2>
          <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6 text-sm sm:text-base lg:text-lg text-gray-700 leading-relaxed px-4 sm:px-0">
            <p>
              Từ năm 2018, TuTiMi được thành lập với tâm huyết mang đến những ly
              trà sữa chất lượng cao, được chế biến từ nguyên liệu tươi ngon
              nhất được tuyển chọn kỹ lưỡng.
            </p>
            <p>
              Chúng tôi cam kết sử dụng 100% nguyên liệu tự nhiên, từ lá trà cao
              cấp đến sữa tươi nguyên chất, tạo nên hương vị đặc trưng và an
              toàn cho sức khỏe.
            </p>
            <p>
              Với phong cách phục vụ tận tâm và không gian ấm cúng, TuTiMi không
              chỉ là nơi thưởng thức đồ uống mà còn là điểm đến để kết nối và
              chia sẻ những khoảnh khắc đáng nhớ.
            </p>
            <p className="text-xl font-sans font-semibold text-primary-navy italic">
              "Mỗi giọt hạnh phúc, mỗi ly kết nối, hương thơm gắn kết"
            </p>
          </div>
        </motion.div>

        {/* Gallery - Carousel on mobile, Grid on desktop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <MobileCarousel
            autoplay={true}
            autoplayDelay={4000}
            showDots={true}
            showArrows={false}
            className=""
            slidesToShow={{ mobile: 1, tablet: 2, desktop: 4 }}
          >
            {galleryItems.map((item, index) => (
              <motion.div
                key={index}
                className="group relative overflow-hidden rounded-xl sm:rounded-2xl aspect-square"
                role="img"
                aria-label={item.alt}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={
                  isInView
                    ? { opacity: 1, scale: 1 }
                    : { opacity: 0, scale: 0.8 }
                }
                transition={{
                  duration: 0.6,
                  delay: 0.6 + index * 0.1,
                  ease: "easeOut",
                }}
              >
                <img
                  src={item.src}
                  alt={item.alt}
                  className="w-full h-full object-contain transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 text-white">
                    <p className="font-semibold text-shadow text-sm sm:text-base">
                      {item.title}
                    </p>
                  </div>
                </div>
                {/* Accessibility overlay for screen readers */}
                <div className="sr-only">
                  {item.title}: {item.alt}
                </div>
              </motion.div>
            ))}
          </MobileCarousel>
        </motion.div>
      </Container>
    </Section>
  )
}

export default BrandStory
