import React from "react"
import { motion } from "framer-motion"
import { Smartphone, Download, QrCode, Clock } from "lucide-react"
import {
  Section,
  Container,
  LiquidGlassCard,
  LiquidGlassButton,
} from "../shared/LiquidGlassComponents"
import { useScrollAnimation } from "../../../hooks/useScrollAnimation"

interface AppIntegrationProps {
  className?: string
}

/**
 * App Integration Section Component
 * Showcases mobile app download options and QR code
 */
const AppIntegration: React.FC<AppIntegrationProps> = ({ className = "" }) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })

  return (
    <Section className={className} id="app-integration">
      <Container>
        <motion.div
          ref={ref}
          className="text-center mb-12 sm:mb-16 px-4 sm:px-0"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-primary-navy mb-4 sm:mb-6">
            Đặt hàng trực tuyến
          </h2>
          <p className="text-sm sm:text-base lg:text-lg text-gray-600">
            Đặt hàng TuTiMi dễ dàng qua các ứng dụng giao hàng phổ biến
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-10 lg:gap-12 items-center px-4 sm:px-0"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {/* Delivery Apps */}
          <motion.div
            className="text-center lg:text-left space-y-6 sm:space-y-8"
            initial={{ opacity: 0, x: -60 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -60 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-xl sm:text-2xl font-bold text-primary-navy">
                Đặt hàng qua ứng dụng giao hàng
              </h3>
              <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                TuTiMi có mặt trên các nền tảng giao hàng hàng đầu. Đặt hàng dễ
                dàng và nhận đồ uống yêu thích tại nhà.
              </p>

              {/* Delivery Features */}
              <ul className="text-gray-600 space-y-2 text-left text-sm sm:text-base">
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary-navy rounded-full flex-shrink-0"></div>
                  Giao hàng nhanh chóng trong 30-45 phút
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary-navy rounded-full flex-shrink-0"></div>
                  Theo dõi đơn hàng real-time
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary-navy rounded-full flex-shrink-0"></div>
                  Nhiều phương thức thanh toán tiện lợi
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary-navy rounded-full flex-shrink-0"></div>
                  Ưu đãi và khuyến mãi độc quyền
                </li>
              </ul>
            </div>
          </motion.div>

          {/* Delivery Platforms */}
          <motion.div
            className="flex justify-center"
            initial={{ opacity: 0, x: 60 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 60 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <LiquidGlassCard className="p-4 sm:p-6 lg:p-8 text-center w-full max-w-md">
              <h4 className="text-lg sm:text-xl font-bold text-primary-navy mb-4 sm:mb-6">
                Có mặt trên các nền tảng
              </h4>

              <div className="flex flex-row gap-3 sm:gap-4 justify-center mb-4 sm:mb-6">
                <LiquidGlassButton
                  href="https://food.grab.com"
                  variant="secondary"
                  className="group w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 !p-0 flex items-center justify-center"
                  aria-label="Đặt hàng TuTiMi qua GrabFood"
                >
                  <img
                    src="/logos/grabfood.png"
                    alt="GrabFood"
                    className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 object-contain group-hover:scale-110 transition-transform"
                  />
                </LiquidGlassButton>

                <LiquidGlassButton
                  href="https://shopeefood.vn"
                  variant="secondary"
                  className="group w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 !p-0 flex items-center justify-center"
                  aria-label="Đặt hàng TuTiMi qua ShopeeFood"
                >
                  <img
                    src="/logos/shopeefoodvn.png"
                    alt="ShopeeFood"
                    className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 object-contain group-hover:scale-110 transition-transform"
                  />
                </LiquidGlassButton>

                <LiquidGlassButton
                  href="https://food.be.com.vn/"
                  variant="secondary"
                  className="group w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 !p-0 flex items-center justify-center"
                  aria-label="Đặt hàng TuTiMi qua BeFood"
                >
                  <img
                    src="/logos/befood.png"
                    alt="BeFood"
                    className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 object-contain group-hover:scale-110 transition-transform"
                  />
                </LiquidGlassButton>
              </div>

              <div className="space-y-1 sm:space-y-2">
                <p className="text-sm sm:text-base text-gray-700 font-semibold">
                  Đặt hàng dễ dàng
                </p>
                <p className="text-xs sm:text-sm text-gray-500">
                  Tìm kiếm "TuTiMi" trên ứng dụng giao hàng yêu thích
                </p>
              </div>
            </LiquidGlassCard>
          </motion.div>
        </motion.div>

        {/* Delivery Benefits Cards */}
        <motion.div
          className="mt-12 sm:mt-16 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 px-4 sm:px-0"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, delay: 1.0 }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={
              isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }
            }
            transition={{ duration: 0.6, delay: 1.2 }}
          >
            <LiquidGlassCard className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Clock size={32} className="text-white" />
              </div>
              <h4 className="text-lg font-semibold text-primary-navy mb-2">
                Giao hàng nhanh
              </h4>
              <p className="text-gray-600 text-sm">
                Đồ uống được giao trong 30-45 phút, đảm bảo chất lượng tươi ngon
              </p>
            </LiquidGlassCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={
              isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }
            }
            transition={{ duration: 0.6, delay: 1.4 }}
          >
            <LiquidGlassCard className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Smartphone size={32} className="text-white" />
              </div>
              <h4 className="text-lg font-semibold text-primary-navy mb-2">
                Theo dõi đơn hàng
              </h4>
              <p className="text-gray-600 text-sm">
                Cập nhật trạng thái đơn hàng real-time từ lúc chuẩn bị đến giao
                hàng
              </p>
            </LiquidGlassCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={
              isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }
            }
            transition={{ duration: 0.6, delay: 1.6 }}
          >
            <LiquidGlassCard className="p-6 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <QrCode size={32} className="text-white" />
              </div>
              <h4 className="text-lg font-semibold text-primary-navy mb-2">
                Thanh toán đa dạng
              </h4>
              <p className="text-gray-600 text-sm">
                Hỗ trợ nhiều phương thức: tiền mặt, thẻ, ví điện tử, chuyển
                khoản
              </p>
            </LiquidGlassCard>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  )
}

export default AppIntegration
