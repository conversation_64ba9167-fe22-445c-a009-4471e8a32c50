import React from "react"
import { Phone, MessageCircle, Clock } from "lucide-react"
import { ZaloIcon, MessengerIcon } from "@/components/icons"
import { CONTACT, SOCIAL_MEDIA } from "../../../constants"
import {
  Section,
  Container,
  LiquidGlassCard,
  LiquidGlassButton,
} from "../shared/LiquidGlassComponents"

interface CustomerSupportProps {
  className?: string
}

/**
 * Customer Support Section Component
 * Displays various customer support channels and contact methods
 */
const CustomerSupport: React.FC<CustomerSupportProps> = ({
  className = "",
}) => {
  return (
    <Section
      className={`bg-gradient-to-br from-primary-navy/5 to-blue-100/50 ${className}`}
      id="customer-support"
    >
      <Container>
        <div className="text-center mb-12 sm:mb-16 px-4 sm:px-0">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-sans font-bold text-primary-navy mb-4 sm:mb-6">
            Hỗ trợ khách hàng
          </h2>
          <p className="text-sm sm:text-base lg:text-lg text-gray-600">
            Chúng tôi luôn sẵn sàng lắng nghe và hỗ trợ bạn 24/7
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 items-stretch px-4 sm:px-0">
          {/* Zalo */}
          <LiquidGlassCard className="p-4 sm:p-6 lg:p-8 text-center flex flex-col min-h-[300px] sm:min-h-[350px] lg:min-h-[400px]">
            <div className="flex flex-col items-center flex-grow">
              <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <ZaloIcon
                  size={24}
                  className="text-white sm:w-7 sm:h-7 lg:w-8 lg:h-8"
                />
              </div>
              <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3 sm:mb-4">
                Chat Zalo
              </h3>
              <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed">
                Nhắn tin trực tiếp với chúng tôi qua Zalo để được hỗ trợ nhanh
                chóng và tiện lợi
              </p>
              <div className="space-y-2 sm:space-y-3 text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6">
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Phản hồi trong 5 phút</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Hỗ trợ đặt hàng</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Tư vấn sản phẩm</span>
                </div>
              </div>
            </div>
            <div className="mt-auto">
              <LiquidGlassButton
                href={`https://zalo.me/${CONTACT.PHONE}`}
                variant="secondary"
                className="w-full text-sm sm:text-base"
                aria-label="Chat với TuTiMi qua Zalo"
              >
                <ZaloIcon size={16} className="sm:w-[18px] sm:h-[18px]" />
                Chat Zalo
              </LiquidGlassButton>
            </div>
          </LiquidGlassCard>

          {/* Messenger */}
          <LiquidGlassCard className="p-4 sm:p-6 lg:p-8 text-center flex flex-col min-h-[300px] sm:min-h-[350px] lg:min-h-[400px]">
            <div className="flex flex-col items-center flex-grow">
              <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <MessengerIcon
                  size={24}
                  className="sm:w-7 sm:h-7 lg:w-8 lg:h-8"
                />
              </div>
              <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3 sm:mb-4">
                Messenger
              </h3>
              <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed">
                Kết nối với chúng tôi qua Facebook Messenger để được hỗ trợ và
                cập nhật tin tức mới nhất
              </p>
              <div className="space-y-2 sm:space-y-3 text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6">
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Nhận thông báo khuyến mãi</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Góp ý và phản hồi</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Cộng đồng TuTiMi</span>
                </div>
              </div>
            </div>
            <div className="mt-auto">
              <LiquidGlassButton
                href={SOCIAL_MEDIA.FACEBOOK.PAGE}
                variant="secondary"
                className="w-full text-sm sm:text-base"
                aria-label="Chat với TuTiMi qua Facebook Messenger"
              >
                <MessengerIcon size={16} className="sm:w-[18px] sm:h-[18px]" />
                Chat Messenger
              </LiquidGlassButton>
            </div>
          </LiquidGlassCard>

          {/* Hotline */}
          <LiquidGlassCard className="p-4 sm:p-6 lg:p-8 text-center flex flex-col min-h-[300px] sm:min-h-[350px] lg:min-h-[400px]">
            <div className="flex flex-col items-center flex-grow">
              <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-gradient-to-r from-primary-navy to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <Phone
                  size={24}
                  className="text-white sm:w-7 sm:h-7 lg:w-8 lg:h-8"
                />
              </div>
              <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3 sm:mb-4">
                Hotline
              </h3>
              <a
                href="tel:0778676009"
                className="text-xl sm:text-2xl lg:text-3xl font-bold text-primary-navy hover:text-blue-600 transition-colors block mb-3 sm:mb-4 text-center"
                aria-label="Gọi hotline TuTiMi"
              >
                ************
              </a>
              <div className="flex items-center justify-center gap-2 text-gray-600 mb-4 sm:mb-6">
                <Clock size={14} className="sm:w-4 sm:h-4" />
                <span className="text-sm sm:text-base">Hỗ trợ 24/7</span>
              </div>
            </div>
            <div className="mt-auto">
              <LiquidGlassButton
                href="tel:0778676009"
                variant="primary"
                className="w-full text-sm sm:text-base"
                aria-label="Gọi ngay hotline TuTiMi"
              >
                <Phone size={16} className="sm:w-[18px] sm:h-[18px]" />
                Gọi ngay
              </LiquidGlassButton>
            </div>
          </LiquidGlassCard>
        </div>
      </Container>
    </Section>
  )
}

export default CustomerSupport
