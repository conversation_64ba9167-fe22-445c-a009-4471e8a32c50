import React from "react"
import { motion } from "framer-motion"
import { MapPin, Clock, Phone, Mail, FacebookIcon } from "lucide-react"
import { TiktokIcon } from "@/components/icons"
import { CONTACT, MAPS, SOCIAL_MEDIA } from "@/constants"
import {
  LiquidGlassCard,
  LiquidGlassButton,
} from "@/features/he-thong-cua-hang/shared/LiquidGlassComponents"
import { useScrollAnimation } from "@/hooks/useScrollAnimation"

interface FooterProps {
  className?: string
}

/**
 * Footer Section Component
 * Contains company info, contact details, policies, and social media links
 */
const Footer: React.FC<FooterProps> = ({ className = "" }) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.1 })
  const currentYear = new Date().getFullYear()

  return (
    <footer
      className={`bg-gradient-to-br from-primary-navy to-support-dark text-white py-12 sm:py-16 ${className}`}
    >
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 items-start"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          {/* Company Info */}
          <div className="flex flex-col min-h-[180px] sm:min-h-[200px] text-center sm:text-left lg:col-span-1">
            <div className="flex-grow">
              <h3 className="text-xl sm:text-2xl font-bold mb-3 sm:mb-4 text-white">
                TuTiMi
              </h3>
              <p className="text-gray-300 mb-3 sm:mb-4 leading-relaxed text-sm sm:text-base">
                Tỉ mỉ tận tâm – Nâng tầm vị giác. Mỗi giọt hạnh phúc, mỗi ly kết
                nối, hương thơm gắn kết.
              </p>
              <div className="space-y-2 text-gray-300 text-sm sm:text-base">
                <div className="flex items-start gap-2 sm:gap-3 justify-center sm:justify-start">
                  <MapPin
                    size={16}
                    className="text-white mt-0.5 flex-shrink-0 sm:w-[18px] sm:h-[18px]"
                    aria-hidden="true"
                  />
                  <span className="text-left">{CONTACT.ADDRESS}</span>
                </div>
                <div className="flex items-center gap-2 sm:gap-3 justify-center sm:justify-start">
                  <Clock
                    size={16}
                    className="text-white flex-shrink-0 sm:w-[18px] sm:h-[18px]"
                    aria-hidden="true"
                  />
                  <span>
                    <span className="sr-only">Giờ hoạt động: </span>
                    Mở cửa 6:00 - 23:00 hàng ngày
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Contact */}
          <div className="flex flex-col min-h-[200px] text-center md:text-left">
            <div className="flex-grow">
              <h4 className="text-xl font-bold mb-4 text-white">Liên hệ</h4>
              <ul className="space-y-3 text-gray-300">
                <li className="flex justify-center md:justify-start">
                  <a
                    href={CONTACT.PHONE_URL}
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300 flex items-center gap-2"
                    aria-label="Gọi điện thoại đến TuTiMi"
                  >
                    <Phone size={16} aria-hidden="true" />
                    {CONTACT.PHONE}
                  </a>
                </li>
                <li className="flex justify-center md:justify-start">
                  <a
                    href={CONTACT.EMAIL_URL}
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300 flex items-center gap-2"
                    aria-label="Gửi email đến TuTiMi"
                  >
                    <Mail size={16} aria-hidden="true" />
                    {CONTACT.EMAIL}
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Quick Links */}
          <div className="flex flex-col min-h-[200px] text-center md:text-left">
            <div className="flex-grow">
              <h4 className="text-xl font-bold mb-4 text-white">
                Liên kết nhanh
              </h4>
              <ul className="space-y-3 text-gray-300">
                <li className="flex justify-center md:justify-start">
                  <a
                    href="#hero"
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300"
                    aria-label="Chuyển đến phần Hero"
                  >
                    Trang chủ
                  </a>
                </li>
                <li className="flex justify-center md:justify-start">
                  <a
                    href="#brand-story"
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300"
                    aria-label="Chuyển đến phần Câu chuyện thương hiệu"
                  >
                    Câu chuyện thương hiệu
                  </a>
                </li>
                <li className="flex justify-center md:justify-start">
                  <a
                    href="#menu"
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300"
                    aria-label="Chuyển đến phần Thực đơn"
                  >
                    Thực đơn
                  </a>
                </li>
                <li className="flex justify-center md:justify-start">
                  <a
                    href="#customer-feedback"
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300"
                    aria-label="Chuyển đến phần Phản hồi khách hàng"
                  >
                    Phản hồi khách hàng
                  </a>
                </li>
                <li className="flex justify-center md:justify-start">
                  <a
                    href="#store-locator"
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300"
                    aria-label="Chuyển đến phần Tìm cửa hàng"
                  >
                    Tìm cửa hàng
                  </a>
                </li>
                <li className="flex justify-center md:justify-start">
                  <a
                    href="#app-integration"
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300"
                    aria-label="Chuyển đến phần Đặt hàng trực tuyến"
                  >
                    Đặt hàng trực tuyến
                  </a>
                </li>
                <li className="flex justify-center md:justify-start">
                  <a
                    href="#customer-support"
                    className="text-gray-300 hover:text-white hover:underline transition-colors duration-300"
                    aria-label="Chuyển đến phần Hỗ trợ khách hàng"
                  >
                    Hỗ trợ khách hàng
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Social Media Links & Copyright */}
        <motion.div
          className="mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-white/20"
          initial={{ opacity: 0, y: 40 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6">
            <div className="flex space-x-4 sm:space-x-6">
              <LiquidGlassButton
                href={SOCIAL_MEDIA.FACEBOOK.PAGE}
                variant="glass"
                className="w-10 h-10 sm:w-12 sm:h-12 !p-0 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 hover:scale-110 transition-all duration-300"
                aria-label="Theo dõi TuTiMi trên Facebook"
              >
                <FacebookIcon
                  size={18}
                  className="sm:w-5 sm:h-5 text-white fill-white"
                />
              </LiquidGlassButton>
              <LiquidGlassButton
                href={SOCIAL_MEDIA.TIKTOK}
                variant="glass"
                className="w-10 h-10 sm:w-12 sm:h-12 !p-0 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 hover:scale-110 transition-all duration-300"
                aria-label="Theo dõi TuTiMi trên TikTok"
              >
                <TiktokIcon size={18} className="sm:w-5 sm:h-5 text-white" />
              </LiquidGlassButton>
              <LiquidGlassButton
                href={CONTACT.PHONE_URL}
                variant="glass"
                className="w-10 h-10 sm:w-12 sm:h-12 !p-0 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/20 hover:scale-110 transition-all duration-300"
                aria-label="Gọi điện thoại đến TuTiMi"
              >
                <Phone
                  size={18}
                  className="sm:w-5 sm:h-5 text-white fill-white"
                />
              </LiquidGlassButton>
            </div>

            <div className="text-center text-gray-400">
              <p className="text-sm sm:text-base">
                &copy; {currentYear} TuTiMi. All rights reserved.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
