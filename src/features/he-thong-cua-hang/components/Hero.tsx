import React from "react"
import { motion } from "framer-motion"
import { useScrollAnimation } from "../../../hooks/useScrollAnimation"

interface HeroProps {
  className?: string
}

/**
 * Hero Section Component with Background Image
 * TuTiMi brand presentation with full-screen background image similar to Phela style
 */
const Hero: React.FC<HeroProps> = ({ className = "" }) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })

  return (
    <motion.section
      id="hero"
      ref={ref}
      className={`relative w-full sm:h-screen overflow-hidden group cursor-pointer ${className}`}
      style={{
        width: "100vw",
        marginLeft: "calc(-50vw + 50%)",
        marginRight: "calc(-50vw + 50%)",
        marginTop: 0,
        marginBottom: 0,
      }}
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 1.2, ease: "easeOut" }}
    >
      {/* Mobile: Image tag that fits container to image ratio */}
      <div className="block sm:hidden relative w-full">
        <img
          src="/images/ttmi-cover-wweb.jpg"
          alt="TuTiMi Cover"
          className="w-full h-auto object-contain"
        />
        {/* Smooth hover overlay - dark overlay */}
        <div className="absolute inset-0 w-full h-full bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-700 ease-in-out" />

        {/* Smooth hover overlay - gradient overlay */}
        <div className="absolute inset-0 w-full h-full bg-gradient-to-t from-black/10 via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700 ease-in-out" />

        {/* Bottom brand color overlay - similar to Phela style */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-primary-navy/20 via-primary-navy/10 to-transparent" />
      </div>

      {/* Desktop: Background Image with Full Coverage */}
      <div
        className="hidden sm:block absolute inset-0 w-full h-full bg-contain bg-center bg-no-repeat"
        style={{
          backgroundImage: "url(/images/ttmi-cover-wweb.jpg)",
          minWidth: "100vw",
        }}
      >
        {/* Smooth hover overlay - dark overlay */}
        <div className="absolute inset-0 w-full h-full bg-black opacity-0 group-hover:opacity-20 transition-opacity duration-700 ease-in-out" />

        {/* Smooth hover overlay - gradient overlay */}
        <div className="absolute inset-0 w-full h-full bg-gradient-to-t from-black/10 via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-700 ease-in-out" />

        {/* Bottom brand color overlay - similar to Phela style */}
        <div className="absolute bottom-0 left-0 right-0 h-32 sm:h-40 bg-gradient-to-t from-primary-navy/20 via-primary-navy/10 to-transparent" />
      </div>
    </motion.section>
  )
}

export default Hero
