import React, { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  MapPin,
  Phone,
  ExternalLink,
  Navigation,
  Store,
  ChevronDown,
  ChevronUp,
  Clock,
} from "lucide-react"
import styles from "./StoreLocator.module.css"
import { MAPS, STORE_LOCATIONS_BY_AREA } from "../../../constants"
import { stores } from "../../../constants/stores"
import {
  Section,
  Container,
  LiquidGlassButton,
} from "../shared/LiquidGlassComponents"
import { useScrollAnimation } from "../../../hooks/useScrollAnimation"

// Helper function to get specific Google Maps URL for store or fallback to generated URL
const getStoreMapUrl = (store: { name: string; address: string }): string => {
  // Map store names to their specific URLs
  const storeUrlMap: { [key: string]: string } = {
    "Lê Thị Riêng": "https://maps.app.goo.gl/X5ttgyB6oVWuPp4f9",
    "<PERSON><PERSON><PERSON>": "https://maps.app.goo.gl/2cZDcVB8QUv1vnS38",
    "Đặng Chất": "https://maps.app.goo.gl/h3VJ6qa8STd1Y4C97",
    "Nguyễn Tri Phương": "https://maps.app.goo.gl/CBRw7y1iUCcwVzy5A",
    "Vạn Kiếp": "https://maps.app.goo.gl/VUoLdtcw5nyi4FxDA",
    "Đường số 8": "https://maps.app.goo.gl/fTrCex537v6YWvDXA",
    "Phan Văn Trị": "https://maps.app.goo.gl/ydJYi9CKA2PM9qAu5",
    "Lê Văn Quới": "https://maps.app.goo.gl/8J8MZ9CGqu41YWZS8",
    "Hồ Học Lãm": "https://maps.app.goo.gl/nmo6KqM1LxekMN5T6",
    "Hiệp Bình": "https://maps.app.goo.gl/Ti1QUcUQseQ8c2Hi9",
    "Nguyễn Duy Trinh": "https://maps.app.goo.gl/4KgPGDuN12uifkLa6",
    "Đường Số 17, Thủ Đức": "https://maps.app.goo.gl/Ha3NGHRiBkRf4rwd6",
    "Đỗ Xuân Hợp": "https://maps.app.goo.gl/6sA8cYtdwtV2JyTW6",
    "Ngô Tất Tố":
      "https://www.google.com/maps/place/Tr%C3%A0+S%E1%BB%AFa+TuTiMi+-+71+Ng%C3%B4+T%E1%BA%A5t+T%E1%BB%91,+Ph%C6%B0%E1%BB%9Dng+21,+Qu%E1%BA%ADn+Bi%CC%80nh+Tha%CC%A3nh/@10.7928043,106.7116873,17z/data=!4m15!1m8!3m7!1s0x317528adf5866e8b:0x8ee1e1cc661b8d3b!2zNzEgxJAuIE5nw7QgVOG6pXQgVOG7kSwgUGjGsOG7nW5nIDE5LCBCw6xuaCBUaOG6oW5oLCBI4buTIENow60gTWluaA!3b1!8m2!3d10.7928043!4d106.7116873!16s%2Fg%2F11jzntfnqc!3m5!1s0x31752939b5ed2c27:0xf19d35f9ba47ab6b!8m2!3d10.7928025!4d106.7116893!16s%2Fg%2F11rfn2d8pd?entry=ttu&g_ep=EgoyMDI1MDYyOS4wIKXMDSoASAFQAw%3D%3D",
  }

  // Return specific URL if available, otherwise generate from address
  if (storeUrlMap[store.name]) {
    return storeUrlMap[store.name]
  }

  // Fallback to generated URL
  const encodedAddress = encodeURIComponent(store.address)
  return `https://www.google.com/maps/search/${encodedAddress}`
}

interface StoreLocatorProps {
  className?: string
}

/**
 * Enhanced Store Locator Section Component with Apple's Liquid Glass Design
 * Features sophisticated glassmorphism effects, dynamic interactions, and enhanced visual hierarchy
 */
const StoreLocator: React.FC<StoreLocatorProps> = ({ className = "" }) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })
  const [hoveredStore, setHoveredStore] = useState<number | null>(null)
  const [expandedAreas, setExpandedAreas] = useState<Set<string>>(new Set())
  const [showAllStores, setShowAllStores] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  // Display all stores from the updated stores data
  const storeList = stores

  const toggleArea = (areaName: string) => {
    setExpandedAreas(prev => {
      const newSet = new Set(prev)
      if (newSet.has(areaName)) {
        newSet.delete(areaName)
      } else {
        newSet.add(areaName)
      }
      return newSet
    })
  }

  const handleToggleAllStores = () => {
    if (isAnimating) return

    setIsAnimating(true)
    setShowAllStores(!showAllStores)

    setTimeout(() => {
      setIsAnimating(false)
    }, 600)
  }

  return (
    <Section
      className={`relative overflow-hidden py-20 ${className}`}
      id="store-locator"
    >
      {/* Enhanced Background with Liquid Glass Effects */}
      <div className="absolute inset-0">
        {/* Primary gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-navy/8 via-blue-50/30 to-white/20" />

        {/* Floating glass orbs for depth */}
        <div className="absolute top-20 left-1/4 w-96 h-96 bg-gradient-to-r from-primary-navy/10 to-blue-400/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-300/10 to-primary-navy/10 rounded-full blur-3xl animate-pulse delay-1000" />

        {/* Subtle texture overlay */}
        <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-primary-navy/5 via-transparent to-blue-300/5" />
      </div>

      <Container className="relative z-10">
        {/* Enhanced Header with Liquid Glass Card */}
        <motion.div
          ref={ref}
          className="text-center mb-16"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className="relative">
            {/* Liquid Glass Header Container */}
            <div className="relative bg-white/[0.06] backdrop-blur-3xl border border-white/[0.08] rounded-xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 shadow-[0_8px_32px_rgba(31,65,113,0.12)] mx-4 sm:mx-0">
              <div className="absolute inset-0 rounded-xl sm:rounded-3xl bg-gradient-to-br from-white/[0.08] via-transparent to-primary-navy/[0.02] pointer-events-none" />

              <motion.h2
                className="relative text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-primary-navy mb-3 sm:mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Cửa hàng TuTiMi
              </motion.h2>

              <motion.p
                className="relative text-sm sm:text-base lg:text-lg text-gray-600 mb-3 sm:mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                Tìm cửa hàng TuTiMi gần bạn nhất
              </motion.p>

              {/* Enhanced Store count indicator with Liquid Glass design - Now clickable */}
              <motion.button
                onClick={handleToggleAllStores}
                disabled={isAnimating}
                className={`relative inline-flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 bg-white/[0.06] backdrop-blur-2xl rounded-xl sm:rounded-xl border border-white/[0.12] shadow-[0_4px_24px_rgba(31,65,113,0.08)] hover:bg-white/[0.1] hover:border-white/[0.2] hover:shadow-[0_8px_40px_rgba(31,65,113,0.12)] transition-all duration-500 ease-out group cursor-pointer ${
                  isAnimating ? "pointer-events-none opacity-75" : ""
                } ${
                  showAllStores
                    ? "bg-primary-navy/10 border-primary-navy/20"
                    : ""
                }`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={
                  isInView
                    ? { opacity: 1, scale: 1 }
                    : { opacity: 0, scale: 0.8 }
                }
                transition={{ duration: 0.6, delay: 0.4 }}
                aria-label={
                  showAllStores
                    ? "Ẩn danh sách tất cả cửa hàng"
                    : "Hiển thị tất cả cửa hàng"
                }
              >
                {/* Gradient overlay */}
                <div className="absolute inset-0 rounded-xl sm:rounded-xl bg-gradient-to-br from-white/[0.04] to-transparent opacity-70 group-hover:opacity-100 transition-opacity duration-500" />

                <div className="relative flex items-center justify-between w-full">
                  <div className="flex items-center gap-2 sm:gap-3">
                    <Store
                      size={16}
                      className="text-primary-navy sm:w-5 sm:h-5"
                    />
                    <span className="text-xs sm:text-sm font-semibold text-primary-navy group-hover:text-primary-navy/90 transition-colors duration-300">
                      <span className="hidden sm:inline">
                        {showAllStores ? "Ẩn danh sách" : "Xem tất cả"}{" "}
                        {storeList.length} cửa hàng
                      </span>
                      <span className="sm:hidden">
                        {showAllStores ? "Ẩn" : "Xem"} {storeList.length} cửa
                        hàng
                      </span>
                    </span>
                  </div>
                  <div className="flex items-center justify-center">
                    {showAllStores ? (
                      <ChevronUp
                        size={16}
                        className="text-primary-navy sm:w-5 sm:h-5"
                      />
                    ) : (
                      <ChevronDown
                        size={16}
                        className="text-primary-navy sm:w-5 sm:h-5"
                      />
                    )}
                  </div>
                </div>
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* All Stores Grid Section - Moved above main content */}
        <AnimatePresence>
          {showAllStores && (
            <motion.div
              initial={{ opacity: 0, height: 0, y: 20 }}
              animate={{ opacity: 1, height: "auto", y: 0 }}
              exit={{ opacity: 0, height: 0, y: -20 }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
              className="mb-12 sm:mb-16 lg:mb-20 px-4 sm:px-0"
            >
              <div className="mb-6 sm:mb-8 text-center">
                <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-primary-navy mb-2">
                  Tất cả {storeList.length} cửa hàng TuTiMi
                </h3>
                <p className="text-sm sm:text-base text-gray-600">
                  Danh sách đầy đủ các cửa hàng trên toàn TP.HCM
                </p>
              </div>

              {/* Stores Grid with enhanced mobile responsiveness */}
              <motion.div
                className="grid gap-3 sm:gap-4 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                variants={{
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: {
                      staggerChildren: 0.03,
                      delayChildren: 0.1,
                    },
                  },
                }}
                initial="hidden"
                animate="visible"
              >
                {storeList.map((store, index) => (
                  <motion.div
                    key={store.id}
                    variants={{
                      hidden: { opacity: 0, y: 20, scale: 0.96 },
                      visible: {
                        opacity: 1,
                        y: 0,
                        scale: 1,
                        transition: {
                          duration: 0.4,
                          ease: [0.25, 0.46, 0.45, 0.94],
                          delay: index * 0.02,
                        },
                      },
                    }}
                    onHoverStart={() => setHoveredStore(store.id)}
                    onHoverEnd={() => setHoveredStore(null)}
                    className="group"
                  >
                    <div
                      className={`
                        relative overflow-hidden rounded-xl sm:rounded-2xl lg:rounded-3xl
                        border border-white/20 bg-white/[0.06] backdrop-blur-2xl
                        shadow-[0_8px_32px_rgba(31,65,113,0.08)]
                        transition-all duration-500 ease-out
                        hover:scale-[1.02] hover:shadow-[0_16px_48px_rgba(31,65,113,0.15)]
                        hover:bg-white/[0.1] hover:border-white/[0.3]
                        active:scale-[0.98] touch-manipulation
                        h-[280px] sm:h-[320px] lg:h-[360px]
                        flex flex-col
                        ${
                          hoveredStore === store.id
                            ? "scale-[1.02] shadow-[0_16px_48px_rgba(31,65,113,0.15)]"
                            : ""
                        }
                      `}
                    >
                      {/* Enhanced glass layers for better mobile visibility */}
                      <div className="absolute inset-0 rounded-xl sm:rounded-2xl lg:rounded-3xl bg-gradient-to-br from-white/[0.08] via-white/[0.02] to-transparent" />
                      <div className="absolute inset-0 rounded-xl sm:rounded-2xl lg:rounded-3xl border border-white/30 bg-gradient-to-br from-white/20 via-white/5 to-transparent opacity-60" />

                      {/* Content with improved mobile spacing and flex layout */}
                      <div className="relative z-10 p-3 sm:p-4 lg:p-6 flex flex-col h-full">
                        {/* Store Header */}
                        <div className="mb-3 sm:mb-4">
                          <h4
                            className="text-base sm:text-lg lg:text-xl font-bold text-primary-navy mb-1 group-hover:text-primary-navy/90 transition-colors duration-300 line-clamp-1 h-[20px] sm:h-[24px] lg:h-[28px]"
                            title={store.name}
                          >
                            {store.name}
                          </h4>
                        </div>

                        {/* Store Details with better mobile layout and flex-grow */}
                        <div className="space-y-2 sm:space-y-3 mb-4 sm:mb-6 flex-grow">
                          <div className="flex items-start gap-2 sm:gap-3">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary-navy/10 rounded-lg sm:rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary-navy/15 transition-colors duration-300">
                              <MapPin
                                size={12}
                                className="text-primary-navy sm:w-4 sm:h-4"
                              />
                            </div>
                            <span
                              className="text-gray-700 text-xs sm:text-sm leading-relaxed line-clamp-3 h-[36px] sm:h-[42px]"
                              title={store.address}
                            >
                              {store.address}
                            </span>
                          </div>

                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary-navy/10 rounded-lg sm:rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary-navy/15 transition-colors duration-300">
                              <Phone
                                size={12}
                                className="text-primary-navy sm:w-4 sm:h-4"
                              />
                            </div>
                            <a
                              href={`tel:${store.phone}`}
                              className="text-primary-navy hover:text-primary-navy/80 transition-colors duration-300 font-medium text-xs sm:text-sm"
                              aria-label={`Gọi điện thoại đến ${store.name}`}
                            >
                              {store.phone}
                            </a>
                          </div>

                          <div className="flex items-center gap-2 sm:gap-3">
                            <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary-navy/10 rounded-lg sm:rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary-navy/15 transition-colors duration-300">
                              <Clock
                                size={12}
                                className="text-primary-navy sm:w-4 sm:h-4"
                              />
                            </div>
                            <span className="text-gray-700 text-xs sm:text-sm">
                              Mở cửa {store.hours || "6h-23h"} hàng ngày
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons with improved mobile touch targets - positioned at bottom */}
                        <div className="flex gap-2 sm:gap-3 mt-auto">
                          <LiquidGlassButton
                            href={getStoreMapUrl(store)}
                            variant="primary"
                            className="flex-1 justify-center text-xs sm:text-sm py-2 sm:py-3 rounded-lg sm:rounded-xl min-h-[44px] touch-manipulation"
                            aria-label={`Chỉ đường đến ${store.name}`}
                          >
                            <Navigation size={14} className="sm:w-4 sm:h-4" />
                            <span className="hidden sm:inline">Chỉ đường</span>
                            <span className="sm:hidden">Đường</span>
                          </LiquidGlassButton>

                          <LiquidGlassButton
                            href={`tel:${store.phone}`}
                            variant="secondary"
                            className="w-11 h-11 sm:w-12 sm:h-12 p-0 rounded-lg sm:rounded-xl flex shrink-0 items-center justify-center min-h-[44px] touch-manipulation"
                            aria-label={`Gọi điện đến ${store.name}`}
                          >
                            <Phone
                              size={16}
                              className="sm:w-[18px] sm:h-[18px]"
                            />
                          </LiquidGlassButton>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>

              {/* Collapse Button with improved mobile design and proper centering */}
              <motion.div
                className="mt-8 sm:mt-12 flex justify-center items-center w-full"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.4 }}
              >
                <LiquidGlassButton
                  onClick={handleToggleAllStores}
                  variant="secondary"
                  className={`px-4 sm:px-6 lg:px-8 py-2 sm:py-3 text-sm sm:text-base lg:text-lg min-h-[44px] touch-manipulation ${
                    isAnimating ? "pointer-events-none opacity-75" : ""
                  }`}
                  disabled={isAnimating}
                >
                  <ChevronUp size={18} className="sm:w-5 sm:h-5" />
                  <span className="hidden sm:inline">Thu gọn danh sách</span>
                  <span className="sm:hidden">Thu gọn</span>
                </LiquidGlassButton>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Main Content Grid */}
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 px-4 sm:px-0"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          {/* Enhanced Store List with Liquid Glass Container */}
          <motion.div
            className="relative order-2 lg:order-1 mb-4"
            initial={{ opacity: 0, x: -60 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -60 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            {/* Store List Header */}
            <div className="mb-4 sm:mb-6">
              <h3 className="text-xl sm:text-2xl font-bold text-primary-navy mb-1 sm:mb-2">
                Danh sách cửa hàng theo khu vực
              </h3>
              <p className="text-sm sm:text-base text-gray-600">
                Chọn khu vực để xem các cửa hàng
              </p>
            </div>

            {/* Liquid Glass Scrollable Container */}
            <div className="relative bg-white/[0.04] backdrop-blur-2xl border border-white/[0.08] rounded-xl sm:rounded-3xl shadow-[0_8px_32px_rgba(31,65,113,0.08)] h-[400px] sm:h-[500px] lg:h-[680px]">
              <div className="absolute inset-0 rounded-xl sm:rounded-3xl bg-gradient-to-br from-white/[0.06] to-transparent pointer-events-none" />

              <div
                className={`relative h-full overflow-y-auto space-y-3 sm:space-y-4 p-3 sm:p-4 lg:p-6 ${styles.liquidGlassScroll}`}
              >
                {Object.entries(STORE_LOCATIONS_BY_AREA).map(
                  ([areaName, stores]) => (
                    <motion.div
                      key={areaName}
                      initial={{ opacity: 0, y: 20 }}
                      animate={
                        isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                      }
                      transition={{ duration: 0.6, delay: 0.2 }}
                    >
                      {/* Area Header Button */}
                      <button
                        onClick={() => toggleArea(areaName)}
                        className="w-full flex items-center justify-between p-4 bg-white/[0.08] backdrop-blur-2xl border border-white/[0.15] rounded-lg hover:bg-white/[0.12] hover:border-white/[0.25] transition-all duration-300 text-primary-navy group"
                        aria-expanded={expandedAreas.has(areaName)}
                        aria-controls={`stores-${areaName}`}
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary-navy/10 rounded-lg flex items-center justify-center">
                            <MapPin size={20} className="text-primary-navy" />
                          </div>
                          <span className="font-semibold text-lg">
                            Khu vực {areaName}
                          </span>
                        </div>
                        <span className="group-hover:scale-110 transition-transform duration-300">
                          {expandedAreas.has(areaName) ? (
                            <ChevronUp size={20} />
                          ) : (
                            <ChevronDown size={20} />
                          )}
                        </span>
                      </button>

                      {/* Stores in Area */}
                      <AnimatePresence>
                        {expandedAreas.has(areaName) && (
                          <motion.div
                            id={`stores-${areaName}`}
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="mt-4 flex flex-col gap-4 px-4">
                              {stores.map((store, index) => (
                                <motion.div
                                  key={store.id}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{
                                    duration: 0.3,
                                    delay: index * 0.05,
                                  }}
                                  onHoverStart={() => setHoveredStore(store.id)}
                                  onHoverEnd={() => setHoveredStore(null)}
                                  className={
                                    index === stores.length - 1 ? "mb-4" : ""
                                  }
                                >
                                  <div
                                    className={`
                                  relative group
                                  bg-white/[0.06] backdrop-blur-2xl
                                  border border-white/[0.12] rounded-xl
                                  shadow-[0_4px_24px_rgba(31,65,113,0.08)]
                                  transition-all duration-500 ease-out
                                  ${
                                    hoveredStore === store.id
                                      ? "bg-white/[0.12] border-white/[0.2] shadow-[0_8px_40px_rgba(31,65,113,0.15)] scale-[1.02]"
                                      : ""
                                  }
                                `}
                                  >
                                    <div
                                      className={`
                                    absolute inset-0 rounded-xl transition-opacity duration-500
                                    ${
                                      hoveredStore === store.id
                                        ? "bg-gradient-to-br from-white/[0.08] via-primary-navy/[0.02] to-transparent opacity-100"
                                        : "bg-gradient-to-br from-white/[0.04] to-transparent opacity-70"
                                    }
                                  `}
                                    />

                                    <div className="relative p-4">
                                      {/* Store Header */}
                                      <div className="flex items-start justify-between mb-3">
                                        <h4 className="text-lg font-bold text-primary-navy group-hover:text-primary-navy/90 transition-colors duration-300">
                                          {store.name}
                                        </h4>
                                      </div>

                                      {/* Store Details */}
                                      <div className="space-y-2 mb-4">
                                        <div className="flex items-start gap-2">
                                          <div className="w-6 h-6 bg-primary-navy/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                            <MapPin
                                              size={12}
                                              className="text-primary-navy"
                                            />
                                          </div>
                                          <span className="text-gray-700 text-xs leading-relaxed">
                                            {store.address}
                                          </span>
                                        </div>

                                        <div className="flex items-center gap-2">
                                          <div className="w-6 h-6 bg-primary-navy/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                            <Phone
                                              size={12}
                                              className="text-primary-navy"
                                            />
                                          </div>
                                          <a
                                            href={`tel:${store.phone}`}
                                            className="text-primary-navy hover:text-primary-navy/80 transition-colors duration-300 font-medium text-xs"
                                            aria-label={`Gọi điện thoại đến ${store.name}`}
                                          >
                                            {store.phone}
                                          </a>
                                        </div>

                                        <div className="flex items-center gap-2">
                                          <div className="w-6 h-6 bg-primary-navy/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                            <Clock
                                              size={12}
                                              className="text-primary-navy"
                                            />
                                          </div>
                                          <span className="text-gray-700 text-xs">
                                            Mở cửa {store.hours} hàng ngày
                                          </span>
                                        </div>
                                      </div>

                                      {/* Enhanced Action Buttons */}
                                      <div className="flex gap-2">
                                        <LiquidGlassButton
                                          href={getStoreMapUrl(store)}
                                          variant="primary"
                                          className="flex-1 justify-center text-xs py-2 rounded-xl"
                                          aria-label={`Chỉ đường đến ${store.name}`}
                                        >
                                          <Navigation size={12} />
                                          Chỉ đường
                                        </LiquidGlassButton>

                                        <LiquidGlassButton
                                          href={`tel:${store.phone}`}
                                          variant="secondary"
                                          className="w-8 h-full p-0 rounded-xl flex shrink-0 items-center justify-center"
                                          aria-label={`Gọi điện đến ${store.name}`}
                                        >
                                          <Phone size={18} />
                                        </LiquidGlassButton>
                                      </div>
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  )
                )}
              </div>
            </div>
          </motion.div>

          {/* Enhanced Google Maps with Liquid Glass Design */}
          <motion.div
            className="relative order-1 lg:order-2"
            initial={{ opacity: 0, x: 60 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 60 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            {/* Maps Header */}
            <div className="mb-4 sm:mb-6">
              <h3 className="text-xl sm:text-2xl font-bold text-primary-navy mb-1 sm:mb-2">
                Bản đồ cửa hàng
              </h3>
              <p className="text-sm sm:text-base text-gray-600">
                Xem vị trí tất cả cửa hàng TuTiMi
              </p>
            </div>

            {/* Enhanced Maps Container with Liquid Glass */}
            <div className="relative bg-white/[0.04] backdrop-blur-2xl border border-white/[0.08] rounded-xl sm:rounded-3xl p-3 sm:p-4 lg:p-6 shadow-[0_8px_32px_rgba(31,65,113,0.08)] h-[300px] sm:h-[400px] lg:h-[680px]">
              <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-white/[0.06] to-transparent pointer-events-none" />

              {/* Maps Controls Overlay */}
              <div className="relative z-10 mb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary-navy/10 rounded-xl flex items-center justify-center">
                      <MapPin size={16} className="text-primary-navy" />
                    </div>
                    <span className="text-sm font-medium text-primary-navy">
                      {storeList.length} địa điểm
                    </span>
                  </div>

                  <LiquidGlassButton
                    href={MAPS.SEARCH_URL}
                    variant="secondary"
                    className="text-xs px-4 py-2"
                    aria-label="Mở bản đồ trong tab mới"
                  >
                    <ExternalLink size={14} />
                    Mở rộng
                  </LiquidGlassButton>
                </div>
              </div>

              {/* Maps iframe with enhanced styling */}
              <div className="relative h-[calc(100%-4rem)] rounded-xl overflow-hidden">
                {/* Loading overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-navy/5 to-blue-50/20 backdrop-blur-sm flex items-center justify-center z-10">
                  <div className="text-primary-navy/60 text-sm">
                    Đang tải bản đồ...
                  </div>
                </div>

                <iframe
                  src={MAPS.EMBED_URL}
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="Bản đồ vị trí các cửa hàng TuTiMi"
                  className="relative z-20 rounded-xl"
                  onLoad={e => {
                    // Hide loading overlay when map loads
                    const loadingOverlay = e.currentTarget
                      .previousElementSibling as HTMLElement
                    if (loadingOverlay) {
                      loadingOverlay.style.display = "none"
                    }
                  }}
                />
              </div>

              {/* Interactive Map Features Indicator */}
              <div className="absolute bottom-8 left-8 right-8 z-30">
                <div className="bg-white/[0.08] backdrop-blur-xl border border-white/[0.12] rounded-xl p-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2 text-primary-navy">
                      <div className="w-2 h-2 bg-primary-navy rounded-full animate-pulse" />
                      <span>Cửa hàng đang hoạt động</span>
                    </div>
                    <div className="text-gray-600">
                      Nhấn vào marker để xem chi tiết
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  )
}

export default StoreLocator
