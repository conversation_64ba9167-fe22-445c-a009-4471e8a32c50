import React from "react"
import { motion } from "framer-motion"
import {
  Section,
  Container,
  LiquidGlassCard,
} from "../shared/LiquidGlassComponents"
import { useScrollAnimation } from "../../../hooks/useScrollAnimation"

interface MenuProps {
  className?: string
}

/**
 * Menu Section Component
 * Showcases TuTiMi's menu with menu.png image
 */
const Menu: React.FC<MenuProps> = ({ className = "" }) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })

  return (
    <Section className={className} id="menu">
      <Container>
        <motion.div
          ref={ref}
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-sans font-bold text-primary-navy mb-4 sm:mb-6">
            Thực đơn <PERSON>
          </h2>
          <p className="text-sm sm:text-base lg:text-lg text-gray-600 max-w-2xl mx-auto px-4 sm:px-0">
            Khám phá thực đơn đa dạng với những món đồ uống thơm ngon, được chế
            biến từ nguyên liệu tươi ngon nhất
          </p>
        </motion.div>

        {/* Menu Image */}
        <motion.div
          className="flex justify-center px-4 sm:px-0"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={
            isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }
          }
          transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
        >
          <LiquidGlassCard className="p-4 sm:p-6 md:p-8 max-w-4xl w-full">
            <div className="relative">
              <img
                src="/images/menu.jpg"
                alt="Thực đơn TuTiMi - Menu đồ uống đa dạng"
                className="w-full h-auto rounded-lg sm:rounded-xl shadow-2xl"
                loading="lazy"
              />

              {/* Overlay gradient for better text readability if needed */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent rounded-lg sm:rounded-xl pointer-events-none" />
            </div>
          </LiquidGlassCard>
        </motion.div>

        {/* Optional CTA section */}
        <motion.div
          className="text-center mt-8 sm:mt-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <p className="text-sm sm:text-base text-gray-600 mb-4">
            Đặt hàng ngay để thưởng thức những món đồ uống tuyệt vời
          </p>
        </motion.div>
      </Container>
    </Section>
  )
}

export default Menu
