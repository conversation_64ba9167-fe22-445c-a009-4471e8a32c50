import React from "react"
import { motion } from "framer-motion"
import { Phone, FacebookIcon, Users, Briefcase } from "lucide-react"
import {
  Section,
  Container,
  LiquidGlassCard,
  LiquidGlassButton,
} from "../shared/LiquidGlassComponents"
import { useScrollAnimation } from "../../../hooks/useScrollAnimation"
import { SOCIAL_MEDIA, CONTACT } from "../../../constants"

interface FacebookIntegrationProps {
  className?: string
}

/**
 * Facebook Integration Section Component
 * Clean 3-column layout following TuTiMi design patterns with glassmorphism effects
 */
const FacebookIntegration: React.FC<FacebookIntegrationProps> = ({
  className = "",
}) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })

  return (
    <Section
      className={`relative overflow-hidden py-20 ${className}`}
      id="facebook-integration"
    >
      {/* Enhanced Background with Liquid Glass Effects */}
      <div className="absolute inset-0">
        {/* Primary gradient background matching TuTiMi color scheme */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50/40 via-blue-50/30 to-primary-navy/5" />

        {/* Floating glass orbs for depth */}
        <div className="absolute top-20 left-1/4 w-96 h-96 bg-gradient-to-r from-primary-navy/8 to-secondary-mint/8 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 right-1/4 w-80 h-80 bg-gradient-to-r from-secondary-mint/8 to-primary-navy/8 rounded-full blur-3xl animate-pulse delay-1000" />

        {/* Subtle texture overlay */}
        <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-primary-navy/3 via-transparent to-secondary-mint/3" />
      </div>

      <Container className="relative z-10">
        {/* Enhanced Header */}
        <motion.div
          ref={ref}
          className="text-center mb-16"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className="relative">
            {/* Liquid Glass Header Container */}
            <div className="relative bg-white/[0.06] backdrop-blur-3xl border border-white/[0.08] rounded-2xl sm:rounded-3xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8 shadow-[0_8px_32px_rgba(31,65,113,0.12)] mx-4 sm:mx-0">
              <div className="absolute inset-0 rounded-2xl sm:rounded-3xl bg-gradient-to-br from-white/[0.08] via-transparent to-primary-navy/[0.02] pointer-events-none" />

              <motion.h2
                className="relative text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-primary-navy mb-3 sm:mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Kết nối với TuTiMi
              </motion.h2>

              <motion.p
                className="relative text-sm sm:text-base lg:text-lg text-gray-600 mb-3 sm:mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                Theo dõi fanpage, cơ hội nghề nghiệp và liên hệ hỗ trợ
              </motion.p>
            </div>
          </div>
        </motion.div>

        {/* Three Column Grid - SEKAI Style */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 px-4 sm:px-0"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          {/* Column 1: Fanpage Chính */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 40 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <LiquidGlassCard
              className="p-6 sm:p-8 text-center h-[280px] sm:h-[320px] flex flex-col justify-between"
              hover={true}
            >
              {/* Icon */}
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary-navy rounded-full flex items-center justify-center">
                  <FacebookIcon
                    size={20}
                    className="text-white sm:w-6 sm:h-6"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="flex-grow flex flex-col justify-center">
                <p className="text-sm sm:text-base text-gray-600 mb-2">
                  Theo dõi chúng tôi
                </p>
                <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3">
                  TuTiMi FanPage
                </h3>
                <p className="text-xs sm:text-sm text-gray-500 mb-4">
                  @trasuatutimi
                </p>
                <p className="text-xs sm:text-sm text-gray-500 mb-4">
                  Cập nhật menu mới - Khuyến mãi hấp dẫn
                </p>
              </div>

              {/* Button */}
              <LiquidGlassButton
                href={SOCIAL_MEDIA.FACEBOOK.PAGE}
                variant="primary"
                className="w-full text-sm sm:text-base"
                aria-label="Theo dõi TuTiMi FanPage"
              >
                <Users size={16} className="sm:w-[18px] sm:h-[18px]" />
                Theo dõi
              </LiquidGlassButton>
            </LiquidGlassCard>
          </motion.div>

          {/* Column 2: Fanpage Tuyển Dụng */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 40 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <LiquidGlassCard
              className="p-6 sm:p-8 text-center h-[280px] sm:h-[320px] flex flex-col justify-between"
              hover={true}
            >
              {/* Icon */}
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary-navy rounded-full flex items-center justify-center">
                  <Briefcase size={20} className="text-white sm:w-6 sm:h-6" />
                </div>
              </div>

              {/* Content */}
              <div className="flex-grow flex flex-col justify-center">
                <p className="text-sm sm:text-base text-gray-600 mb-2">
                  Tham gia với chúng tôi
                </p>
                <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3">
                  TuTiMi Tuyển Dụng
                </h3>
                <p className="text-xs sm:text-sm text-gray-500 mb-4">
                  @tutimituyendung
                </p>
                <p className="text-xs sm:text-sm text-gray-500 mb-4">
                  Cơ hội việc làm - Môi trường thân thiện
                </p>
              </div>

              {/* Button */}
              <LiquidGlassButton
                href={SOCIAL_MEDIA.FACEBOOK.RECRUITMENT}
                variant="secondary"
                className="w-full text-sm sm:text-base"
                aria-label="Xem việc làm TuTiMi"
              >
                <Briefcase size={16} className="sm:w-[18px] sm:h-[18px]" />
                Xem việc làm
              </LiquidGlassButton>
            </LiquidGlassCard>
          </motion.div>

          {/* Column 3: Hotline Hỗ Trợ */}
          <motion.div
            className="relative"
            initial={{ opacity: 0, y: 40 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            <LiquidGlassCard
              className="p-6 sm:p-8 text-center h-[280px] sm:h-[320px] flex flex-col justify-between"
              hover={true}
            >
              {/* Icon */}
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/10 backdrop-blur-xl border border-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-primary-navy rounded-full flex items-center justify-center">
                  <Phone size={20} className="text-white sm:w-6 sm:h-6" />
                </div>
              </div>

              {/* Content */}
              <div className="flex-grow flex flex-col justify-center">
                <p className="text-sm sm:text-base text-gray-600 mb-2">
                  Gọi ngay
                </p>
                <h3 className="text-lg sm:text-xl font-bold text-primary-navy mb-3">
                  ************
                </h3>
                <p className="text-xs sm:text-sm text-gray-500 mb-4">
                  Hỗ trợ 24/7 - Tư vấn miễn phí
                </p>
              </div>

              {/* Button */}
              <LiquidGlassButton
                href="tel:0778676009"
                variant="primary"
                className="w-full text-sm sm:text-base"
                aria-label="Gọi hotline TuTiMi"
              >
                <Phone size={16} className="sm:w-[18px] sm:h-[18px]" />
                Gọi ngay
              </LiquidGlassButton>
            </LiquidGlassCard>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  )
}

export default FacebookIntegration
