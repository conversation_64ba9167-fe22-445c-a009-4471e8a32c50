/* Liquid Glass Scrollbar Styling */
.liquidGlassScroll::-webkit-scrollbar {
  width: 8px;
}

.liquidGlassScroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 12px;
}

.liquidGlassScroll::-webkit-scrollbar-thumb {
  background: rgba(31, 65, 113, 0.15);
  backdrop-filter: blur(12px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.liquidGlassScroll::-webkit-scrollbar-thumb:hover {
  background: rgba(31, 65, 113, 0.25);
  backdrop-filter: blur(16px);
  border-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.1);
}

.liquidGlassScroll::-webkit-scrollbar-thumb:active {
  background: rgba(31, 65, 113, 0.35);
  backdrop-filter: blur(20px);
}

/* Firefox scrollbar styling */
.liquidGlassScroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(31, 65, 113, 0.15) transparent;
}
