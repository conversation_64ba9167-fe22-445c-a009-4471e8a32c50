import React from "react"
import { motion } from "framer-motion"
import { Section, Container } from "../shared/LiquidGlassComponents"
import { useScrollAnimation } from "../../../hooks/useScrollAnimation"
import MobileCarousel from "../shared/MobileCarousel"

interface FeedbackProps {
  className?: string
}

interface FeedbackItem {
  src: string
  title: string
  alt: string
}

/**
 * Customer Feedback Section Component
 * Showcases customer feedback and testimonials with TuTiMi's liquid glass design
 */
const Feedback: React.FC<FeedbackProps> = ({ className = "" }) => {
  const { ref, isInView } = useScrollAnimation({ amount: 0.2 })

  const feedbackItems: FeedbackItem[] = [
    {
      src: "/images/feed-backs/feed-back-1.jpg",
      title: "Khách hàng hài lòng",
      alt: "<PERSON>ản hồi tích cực từ khách hàng về TuTiMi",
    },
    {
      src: "/images/feed-backs/feed-back-2.jpg",
      title: "<PERSON><PERSON>h giá 5 sao",
      alt: "Đ<PERSON>h giá 5 sao từ khách hàng thân thiết",
    },
    {
      src: "/images/feed-backs/feed-back-3.jpg",
      title: "Trải nghiệm tuyệt vời",
      alt: "Khách hàng chia sẻ trải nghiệm tuyệt vời tại TuTiMi",
    },
    {
      src: "/images/feed-backs/feed-back-4.jpg",
      title: "Chất lượng cao",
      alt: "Phản hồi về chất lượng sản phẩm cao của TuTiMi",
    },
    {
      src: "/images/feed-backs/feed-back-5.jpg",
      title: "Dịch vụ tận tâm",
      alt: "Khách hàng khen ngợi dịch vụ tận tâm của TuTiMi",
    },
    {
      src: "/images/feed-backs/feed-back-6.jpg",
      title: "Khuyến nghị bạn bè",
      alt: "Khách hàng khuyến nghị TuTiMi cho bạn bè",
    },
  ]

  return (
    <Section className={className} id="customer-feedback">
      <Container>
        <motion.div
          ref={ref}
          className="text-center mb-12 sm:mb-16"
          initial={{ opacity: 0, y: 60 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-sans font-bold text-primary-navy mb-4 sm:mb-6">
            Khách hàng nói gì về TuTiMi
          </h2>
          <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6 text-sm sm:text-base lg:text-lg text-gray-700 leading-relaxed px-4 sm:px-0">
            <p>
              Hàng nghìn khách hàng đã tin tưởng và lựa chọn TuTiMi. Những phản
              hồi tích cực từ khách hàng chính là động lực để chúng tôi không
              ngừng nâng cao chất lượng sản phẩm và dịch vụ.
            </p>
            <p>
              Từ hương vị đồ uống đến không gian quán, từ thái độ phục vụ đến
              trải nghiệm tổng thể - TuTiMi luôn nhận được sự đánh giá cao từ
              cộng đồng yêu thích trà sữa.
            </p>
            <p className="text-xl font-sans font-semibold text-primary-navy italic">
              "Sự hài lòng của khách hàng là thành công của chúng tôi"
            </p>
          </div>
        </motion.div>

        {/* Feedback Gallery - Carousel on mobile, Grid on desktop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <MobileCarousel
            autoplay={true}
            autoplayDelay={4000}
            showDots={true}
            showArrows={false}
            className=""
          >
            {feedbackItems.map((item, index) => (
              <motion.div
                key={index}
                className="group relative overflow-hidden rounded-xl sm:rounded-2xl aspect-[4/3]"
                role="img"
                aria-label={item.alt}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={
                  isInView
                    ? { opacity: 1, scale: 1 }
                    : { opacity: 0, scale: 0.8 }
                }
                transition={{
                  duration: 0.6,
                  delay: 0.6 + index * 0.1,
                  ease: "easeOut",
                }}
              >
                <img
                  src={item.src}
                  alt={item.alt}
                  className="w-full h-full object-contain transition-transform duration-500 group-hover:scale-110"
                  loading="lazy"
                />

                {/* Hover overlay with glassmorphism */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 text-white">
                    <p className="font-semibold text-shadow text-sm sm:text-base">
                      {item.title}
                    </p>
                  </div>
                </div>

                {/* Accessibility overlay for screen readers */}
                <div className="sr-only">
                  {item.title}: {item.alt}
                </div>
              </motion.div>
            ))}
          </MobileCarousel>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-12 sm:mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <p className="text-sm sm:text-base lg:text-lg text-gray-600 mb-6">
            Hãy trở thành một phần của cộng đồng TuTiMi và chia sẻ trải nghiệm
            của bạn
          </p>
        </motion.div>
      </Container>
    </Section>
  )
}

export default Feedback
