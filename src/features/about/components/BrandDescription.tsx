import React from "react"
import { motion } from "framer-motion"

const BrandDescription: React.FC = () => {
  return (
    <section className="w-full bg-white py-4 sm:py-6 md:py-8">
      <div className="max-w-4xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary-navy leading-tight mb-8">
            DANH MỤC MÓN NƯỚC BAO GỒM
          </h2>
        </motion.div>
      </div>
    </section>
  )
}

export default BrandDescription
