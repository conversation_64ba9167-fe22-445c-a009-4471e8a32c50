import React from "react"
import { motion } from "framer-motion"

const FinalMessage: React.FC = () => {
  return (
    <section className="w-full bg-white py-8 sm:py-12 md:py-16">
      <div className="max-w-4xl mx-auto px-4">
        {/* Closing Statement */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-primary-navy leading-tight">
            TuTiMi tự hào biến món uống tưởng chừng quen thuộc thành một trải
            nghiệm đặc sắc, khiến bất kỳ ai đã thử qua đều muốn quay lại để cảm
            nhận lại sự kết hợp tuyệt vời <PERSON>y thêm lần nữa.
          </h2>
        </motion.div>

        {/* Bottom Spacer */}
      </div>
    </section>
  )
}

export default FinalMessage
