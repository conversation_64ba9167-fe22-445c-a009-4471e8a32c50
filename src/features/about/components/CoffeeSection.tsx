import React from "react"
import { motion } from "framer-motion"
import { LiquidGlassButton } from "../../he-thong-cua-hang/shared/LiquidGlassComponents"

const CoffeeSection: React.FC = () => {
  return (
    <section className="w-full bg-white py-8 sm:py-12 md:py-16">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
          {/* Text Content - Left Side */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="order-2 lg:order-1"
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary-navy mb-6 leading-tight text-center md:text-left">
              MILO DẦM
            </h2>

            <div className="space-y-6">
              <h3 className="text-[1.2em] font-bold text-primary-navy leading-[1.33333em] mb-4 text-center md:text-left">
                Đậm vị, đậm chất riêng của TuTiMi
              </h3>

              <h6 className="text-[1.13333em] text-primary-navy leading-[1.33333em] text-center md:text-left">
                Là món best seller được yêu thích nhất tại TuTiMi, Milo Dầm
                chinh phục vị giác ngay từ ngụm đầu tiên với sự hòa quyện đầy mê
                hoặc giữa cacao đậm đà, sữa tươi béo mịn và lớp đá dầm mát lạnh.
                Mỗi ly Milo Dầm không chỉ là thức uống, mà còn là 'tuyên ngôn'
                hương vị đậm chất riêng, độc đáo và đầy năng lượng.
              </h6>

              <div className="mt-6 mb-6">
                <h6 className="text-[1.13333em] text-primary-navy leading-[1.33333em] text-center md:text-left">
                  TuTiMi tự hào biến món uống tưởng chừng quen thuộc thành một
                  trải nghiệm đặc sắc, khiến bất kỳ ai đã thử qua đều muốn quay
                  lại để cảm nhận lại sự kết hợp tuyệt vời ấy thêm lần nữa.
                </h6>
              </div>

              <div className="pt-6 flex justify-center md:justify-start">
                <LiquidGlassButton
                  href="/menu"
                  variant="primary"
                  className="inline-block px-8 py-3 text-lg font-semibold max-w-[200px] hover:scale-100"
                  aria-label="Xem menu MILO DẦM"
                >
                  MENU
                </LiquidGlassButton>
              </div>
            </div>
          </motion.div>

          {/* Image - Right Side */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="order-1 lg:order-2"
          >
            <div className="relative w-4/5 max-w-md mx-auto aspect-[3/4] rounded-lg overflow-hidden shadow-lg">
              <img
                src="/web-ttmi/about-us/milo-dam.jpg"
                alt="TuTiMi Milo Dầm"
                className="absolute inset-0 w-full h-full object-cover"
              />
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default CoffeeSection
