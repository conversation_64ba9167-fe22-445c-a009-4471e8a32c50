import React from "react"
import { motion } from "framer-motion"

const Hero: React.FC = () => {
  return (
    <section className="w-full bg-white">
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="flex flex-col items-center"
      >
        {/* Spacer */}
        <div className="h-4 md:h-8"></div>

        {/* Spacer */}
        <div className="h-8 md:h-12"></div>

        {/* Main Banner Image */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="w-full px-4 md:px-5"
        >
          <img
            src="/images/ttmi-cover-wweb.jpg"
            alt="TuTiMi Cover"
            className="w-full h-auto object-cover rounded-lg shadow-lg hover:scale-105 transition-transform duration-500 ease-in-out"
          />
        </motion.div>
      </motion.div>
    </section>
  )
}

export default Hero
