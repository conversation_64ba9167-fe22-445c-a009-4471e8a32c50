import React from "react"
import { motion } from "framer-motion"

const Statistics: React.FC = () => {
  return (
    <section className="w-full bg-white py-8 sm:py-12 md:py-16">
      <div className="max-w-4xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          {/* Main Title */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-8 md:mb-12 lg:mb-16"
          >
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary-navy leading-tight m-0 p-0">
              H<PERSON><PERSON> trình chinh phục phong vị mới
            </h1>
          </motion.div>

          <div className="space-y-6">
            <p className="text-lg md:text-xl text-primary-navy leading-relaxed">
              TuTiMi là chuỗi trà sữa mang trong mình linh hồn của đại dương,
              lấy cảm hứng từ biển cả và phong cách sống hiện đại. Sắc xanh
              dương đậm phối cùng trắng tinh khôi là dấu ấn nhận diện đặc trưng,
              thể hiện tinh thần trẻ trung, mạnh mẽ và đầy năng lượng. Linh vật
              cá voi biểu tượng của sức mạnh và sự gắn kết chính là hiện thân
              cho tinh thần đoàn kết và khát vọng vươn xa mà TuTiMi luôn hướng
              đến.
            </p>
            <p className="text-lg md:text-xl text-primary-navy leading-relaxed">
              Với mong muốn mang đến cho khách hàng một chuyến phiêu lưu vị giác
              đầy cảm hứng, mỗi ly nước tại TuTiMi đều được hoàn thiện bằng sự
              đồng lòng và tận tâm của từng mảnh ghép những con người cùng chung
              niềm đam mê kiến tạo trải nghiệm trọn vẹn, ngọt lành và đáng nhớ.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Statistics
