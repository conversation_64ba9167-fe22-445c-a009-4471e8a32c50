import React from "react"
import { motion } from "framer-motion"
import { LiquidGlassButton } from "../../he-thong-cua-hang/shared/LiquidGlassComponents"

const TeaSection: React.FC = () => {
  return (
    <section className="w-full bg-white py-8 sm:py-12 md:py-16">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
          {/* Image - Left Side */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="order-1"
          >
            <div className="relative w-4/5 max-w-md mx-auto aspect-[3/4] rounded-lg overflow-hidden shadow-lg">
              <img
                src="/web-ttmi/about-us/tra.jpg"
                alt="TuTiMi Tea"
                className="absolute inset-0 w-full h-full object-cover"
              />
            </div>
          </motion.div>

          {/* Text Content - Right Side */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="order-2"
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-primary-navy mb-6 leading-tight text-center md:text-left">
              TRÀ
            </h2>

            <div className="space-y-6">
              <h3 className="text-[1.2em] font-bold text-primary-navy leading-[1.33333em] mb-4 text-center md:text-left">
                Hành trình từ thiên nhiên đến vị giác
              </h3>

              <h6 className="text-[1.13333em] text-primary-navy leading-[1.33333em] text-center md:text-left">
                Tại TuTiMi, mỗi lá trà đều được tuyển chọn kỹ lưỡng từ những
                vùng trà nổi tiếng Lâm Đồng, nơi khí hậu trong lành và đất trời
                hòa quyện tạo nên hương vị thanh thuần đặc biệt. Đó là khởi đầu
                cho hành trình khám phá vị giác, nơi nghệ thuật pha chế được đặt
                trọn vẹn trong từng ly trà.
              </h6>

              <ul className="space-y-4 text-center md:text-left">
                <li className="text-[1.13333em] text-primary-navy leading-[1.33333em]">
                  <span className="font-bold text-primary-navy">TRÀ SỮA</span> –
                  Dòng sản phẩm làm nên bản sắc TuTiMi. Sự hòa quyện giữa vị trà
                  đậm đà và sữa thơm mịn tạo nên bản giao hưởng hài hòa vừa mềm
                  mại, vừa sâu lắng. Đây chính là dòng sản phẩm chủ đạo chinh
                  phục hàng ngàn tín đồ yêu vị ngọt, là lựa chọn nhất định phải
                  thử trong hành trình ghé thăm TuTiMi.
                </li>

                <li className="text-[1.13333em] text-primary-navy leading-[1.33333em]">
                  <span className="font-bold text-primary-navy">
                    TRÀ TRÁI CÂY
                  </span>{" "}
                  – Thanh vị tươi mát. Kết hợp giữa hương trà thanh mát và vị
                  ngọt dịu của trái cây tự nhiên, Trà Trái Cây tại TuTiMi mang
                  đến cảm giác sảng khoái, tươi mới như được chạm vào hơi thở
                  của đại dương. Mỗi ngụm trà là một lần làm mới vị giác, một
                  trải nghiệm thú vị khó quên.
                </li>
              </ul>

              <div className="pt-6 flex justify-center md:justify-start">
                <LiquidGlassButton
                  href="#menu"
                  variant="primary"
                  className="inline-block px-8 py-3 text-lg font-medium max-w-[200px] hover:scale-100"
                  aria-label="Xem menu trà sữa và trà trái cây"
                >
                  MENU
                </LiquidGlassButton>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default TeaSection
